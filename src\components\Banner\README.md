# Banner 组件

页面顶部横幅组件，支持背景图片和标题文字。

## 功能特性

- 支持自定义背景图片
- 支持自定义标题文字和样式
- 支持调整Banner高度
- 内置遮罩层，确保文字可读性
- 响应式设计
- 可复用的组件设计

## 使用示例

```tsx
import Banner from '@/components/Banner';

// 基础用法
<Banner
  backgroundImage="/images/data-visualization-banner.jpg"
  title="数字化统计分析"
/>

// 自定义高度和样式
<Banner
  backgroundImage="/images/banner.jpg"
  title="页面标题"
  height="300px"
  titleLevel={2}
  titleFontSize="3rem"
  overlayOpacity={0.6}
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| backgroundImage | string | - | 背景图片URL |
| title | string | - | 标题文字 |
| height | number \| string | '200px' | Banner高度 |
| titleLevel | 1 \| 2 \| 3 \| 4 \| 5 | 1 | 标题级别 |
| titleFontSize | string | '2.5rem' | 标题字体大小 |
| overlayOpacity | number | 0.4 | 遮罩层透明度 |
| style | React.CSSProperties | - | 自定义样式 |
| titleStyle | React.CSSProperties | - | 标题自定义样式 |

## 设计说明

- 背景图片使用 `cover` 模式，确保图片完全覆盖容器
- 遮罩层使用半透明黑色，提高文字可读性
- 标题文字使用白色，并添加阴影效果
- 组件高度可调，适应不同页面需求
