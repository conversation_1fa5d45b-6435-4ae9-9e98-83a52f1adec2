import { Descriptions, Modal, Tag } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

export interface MountainDetailModalProps {
  visible: boolean;
  record: API.Mountain | null;
  onClose: () => void;
}

export const MountainDetailModal: React.FC<MountainDetailModalProps> = ({
  visible,
  record,
  onClose,
}) => {
  if (!record) return null;

  return (
    <Modal
      title={`山塬详情 - ${record.name}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <Descriptions
        bordered
        column={2}
        size="middle"
        labelStyle={{ width: '120px', fontWeight: 'bold' }}
      >
        <Descriptions.Item label="ID" span={1}>
          {record.id}
        </Descriptions.Item>
        <Descriptions.Item label="山塬名称" span={1}>
          {record.name}
        </Descriptions.Item>

        <Descriptions.Item label="编号" span={1}>
          {record.code || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="类型" span={1}>
          {record.typeDict?.typeName ? (
            <Tag color="green">{record.typeDict.typeName}</Tag>
          ) : (
            <Tag color="default">未分类</Tag>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="经度" span={1}>
          {record.longitude || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="纬度" span={1}>
          {record.latitude || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="高度(米)" span={1}>
          {record.height ? `${record.height}m` : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="所属区域" span={1}>
          {record.regionDict?.regionName ? (
            <Tag color="orange">{record.regionDict.regionName}</Tag>
          ) : (
            <Tag color="default">未知区域</Tag>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="创建时间" span={1}>
          {record.createdAt
            ? dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={1}>
          {record.updatedAt
            ? dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>

        {record.historicalRecords && (
          <Descriptions.Item label="历史记载" span={2}>
            {record.historicalRecords}
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};
