import DataManagementLayout from '@/components/DataManagementLayout';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Space, Tabs, Typography } from 'antd';
import React from 'react';
import { BatchImportModal } from './components/BatchImportModal';
import { BatchOperationBar } from './components/BatchOperationBar';
import { WaterSystemDetailModal } from './components/WaterSystemDetailModal';
import { WaterSystemForm } from './components/WaterSystemForm';
import { WaterSystemStatistics } from './components/WaterSystemStatistics';
import { WaterSystemTable } from './components/WaterSystemTable';

import { WaterSystemToolbar } from './components/WaterSystemToolbar';
import { useWaterSystemManager } from './hooks/useWaterSystemManager';

const { Title } = Typography;

const AdminWaterSystem: React.FC = () => {
  const manager = useWaterSystemManager();

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和操作按钮 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <Title level={2}>水系管理</Title>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={manager.handleAdd}
          >
            添加水系
          </Button>
          <Button onClick={manager.handleImportModalOpen}>批量导入</Button>
        </Space>
      </div>

      {/* 标签页内容 */}
      <Tabs
        activeKey={manager.activeTab}
        onChange={manager.handleTabChange}
        items={[
          {
            key: 'list',
            label: '数据列表',
            children: (
              <DataManagementLayout
                selectedRegionId={manager.regionFilter}
                onRegionChange={manager.handleRegionFilterChange}
                selectorTitle="区域选择"
                style={{ height: 'calc(100vh - 200px)' }}
              >
                <div>
                  {/* 搜索和筛选工具栏 */}
                  <WaterSystemToolbar
                    searchKeyword={manager.searchKeyword}
                    regionFilter={manager.regionFilter}
                    typeFilter={manager.typeFilter}
                    onSearchKeywordChange={(value) =>
                      manager.handleSearch(value)
                    }
                    onSearch={manager.handleSearch}
                    onRegionFilterChange={manager.handleRegionFilterChange}
                    onTypeFilterChange={manager.handleTypeFilterChange}
                    onRefresh={manager.handleRefresh}
                    onReset={manager.handleReset}
                    showRegionFilter={false}
                  />

                  {/* 批量操作栏 */}
                  <BatchOperationBar
                    selectedRowKeys={manager.selectedRowKeys}
                    onBatchDelete={manager.handleBatchDelete}
                    onClearSelection={manager.handleClearSelection}
                  />

                  {/* 数据表格 */}
                  <WaterSystemTable
                    data={manager.data}
                    loading={manager.loading}
                    pagination={manager.pagination}
                    selectedRowKeys={manager.selectedRowKeys}
                    onView={manager.handleView}
                    onEdit={manager.handleEdit}
                    onDelete={manager.handleDelete}
                    onPageChange={manager.handlePageChange}
                    onSelectionChange={manager.handleSelectionChange}
                  />
                </div>
              </DataManagementLayout>
            ),
          },
          {
            key: 'statistics',
            label: '统计分析',
            children: (
              <WaterSystemStatistics
                statistics={manager.statistics}
                loading={manager.statisticsLoading}
              />
            ),
          },
        ]}
      />

      {/* 添加/编辑表单模态框 */}
      <WaterSystemForm
        visible={manager.modalVisible}
        loading={manager.operationLoading}
        editingItem={manager.editingItem}
        onOk={manager.handleSubmit}
        onCancel={manager.handleModalCancel}
      />

      {/* 批量导入模态框 */}
      <BatchImportModal
        visible={manager.importModalVisible}
        loading={manager.batchLoading}
        onDownloadTemplate={manager.handleDownloadTemplate}
        onPreview={manager.handlePreviewExcel}
        onImport={manager.handleImportExcel}
        onCancel={manager.handleImportModalClose}
      />

      {/* 详情查看模态框 */}
      <WaterSystemDetailModal
        visible={manager.detailModalVisible}
        record={manager.detailRecord}
        onClose={manager.handleDetailModalClose}
      />
    </div>
  );
};

export default AdminWaterSystem;
