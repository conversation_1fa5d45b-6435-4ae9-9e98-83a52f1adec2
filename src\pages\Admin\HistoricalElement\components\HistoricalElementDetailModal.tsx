import DictSelect from '@/components/DictSelect';
import { Descriptions, Modal, Tag } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

export interface HistoricalElementDetailModalProps {
  visible: boolean;
  record: API.HistoricalElement | null;
  onClose: () => void;
}

export const HistoricalElementDetailModal: React.FC<
  HistoricalElementDetailModalProps
> = ({ visible, record, onClose }) => {
  if (!record) return null;

  return (
    <Modal
      title={`历史要素详情 - ${record.name}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnHidden
    >
      <Descriptions
        bordered
        column={2}
        size="middle"
        labelStyle={{ width: '120px', fontWeight: 'bold' }}
      >
        <Descriptions.Item label="ID" span={1}>
          {record.id}
        </Descriptions.Item>
        <Descriptions.Item label="历史要素名称" span={1}>
          {record.name}
        </Descriptions.Item>

        <Descriptions.Item label="编号" span={1}>
          {record.code || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="类型" span={1}>
          {record.typeDictId ? (
            <Tag color="green">
              <DictSelect.DictDisplay type="type" id={record.typeDictId} />
            </Tag>
          ) : (
            <Tag color="default">未知类型</Tag>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="建造时间" span={1}>
          {record.constructionTime
            ? dayjs(record.constructionTime).format('YYYY年')
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="所属区域" span={1}>
          {record.regionDictId ? (
            <Tag color="orange">
              <DictSelect.DictDisplay type="region" id={record.regionDictId} />
            </Tag>
          ) : (
            <Tag color="default">未知区域</Tag>
          )}
        </Descriptions.Item>

        <Descriptions.Item label="经度" span={1}>
          {record.constructionLongitude || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="纬度" span={1}>
          {record.constructionLatitude || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="创建时间" span={1}>
          {record.createdAt
            ? dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={1}>
          {record.updatedAt
            ? dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>

        {record.historicalRecords && (
          <Descriptions.Item label="历史记载" span={2}>
            {record.historicalRecords}
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};
