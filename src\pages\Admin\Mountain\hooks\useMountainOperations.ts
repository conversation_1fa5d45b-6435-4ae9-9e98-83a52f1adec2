import {
  batchDeleteMountains,
  batchImportMountains,
  createMountain,
  deleteMountain,
  getMountainImportTemplateInfo,
  importMountainExcelFile,
  previewMountainExcel,
  updateMountain,
} from '@/services/mountain';
import { message } from 'antd';
import { useCallback, useState } from 'react';

export interface UseMountainOperationsReturn {
  // 加载状态
  operationLoading: boolean;
  batchLoading: boolean;

  // CRUD操作
  createElement: (
    params: API.CreateMountainParams,
  ) => Promise<{ success: boolean; message?: string }>;
  updateElement: (
    id: number,
    params: API.UpdateMountainParams,
  ) => Promise<{ success: boolean; message?: string }>;
  deleteElement: (
    id: number,
  ) => Promise<{ success: boolean; message?: string }>;
  batchDeleteElements: (
    ids: number[],
  ) => Promise<{ success: boolean; message?: string }>;

  // 导入导出操作
  downloadTemplate: () => Promise<void>;
  previewExcel: (file: File) => Promise<{
    success: boolean;
    data?: any;
    message?: string;
  }>;
  importExcel: (file: File) => Promise<{
    success: boolean;
    data?: any;
    message?: string;
  }>;
  batchImportElements: (
    elements: API.CreateMountainParams[],
  ) => Promise<{ success: boolean; message?: string }>;
}

export const useMountainOperations = (): UseMountainOperationsReturn => {
  // 加载状态
  const [operationLoading, setOperationLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState(false);

  // 创建山塬
  const createElement = useCallback(
    async (params: API.CreateMountainParams) => {
      setOperationLoading(true);
      try {
        const response = await createMountain(params);

        if (response.errCode === 0) {
          message.success('创建山塬成功');
          return { success: true };
        } else {
          message.error(response.msg || '创建山塬失败');
          return { success: false, message: response.msg };
        }
      } catch (error: any) {
        console.error('创建山塬失败:', error);
        const errorMsg = error?.response?.data?.msg || '创建山塬失败';
        message.error(errorMsg);
        return { success: false, message: errorMsg };
      } finally {
        setOperationLoading(false);
      }
    },
    [],
  );

  // 更新山塬
  const updateElement = useCallback(
    async (id: number, params: API.UpdateMountainParams) => {
      setOperationLoading(true);
      try {
        const response = await updateMountain(id, params);

        if (response.errCode === 0) {
          message.success('更新山塬成功');
          return { success: true };
        } else {
          message.error(response.msg || '更新山塬失败');
          return { success: false, message: response.msg };
        }
      } catch (error: any) {
        console.error('更新山塬失败:', error);
        const errorMsg = error?.response?.data?.msg || '更新山塬失败';
        message.error(errorMsg);
        return { success: false, message: errorMsg };
      } finally {
        setOperationLoading(false);
      }
    },
    [],
  );

  // 删除山塬
  const deleteElement = useCallback(async (id: number) => {
    setOperationLoading(true);
    try {
      const response = await deleteMountain(id);

      if (response.errCode === 0) {
        message.success('删除山塬成功');
        return { success: true };
      } else {
        message.error(response.msg || '删除山塬失败');
        return { success: false, message: response.msg };
      }
    } catch (error: any) {
      console.error('删除山塬失败:', error);
      const errorMsg = error?.response?.data?.msg || '删除山塬失败';
      message.error(errorMsg);
      return { success: false, message: errorMsg };
    } finally {
      setOperationLoading(false);
    }
  }, []);

  // 批量删除山塬
  const batchDeleteElements = useCallback(async (ids: number[]) => {
    setBatchLoading(true);
    try {
      const response = await batchDeleteMountains(ids);

      if (response.errCode === 0) {
        message.success(`成功删除 ${ids.length} 个山塬`);
        return { success: true };
      } else {
        message.error(response.msg || '批量删除失败');
        return { success: false, message: response.msg };
      }
    } catch (error: any) {
      console.error('批量删除失败:', error);
      const errorMsg = error?.response?.data?.msg || '批量删除失败';
      message.error(errorMsg);
      return { success: false, message: errorMsg };
    } finally {
      setBatchLoading(false);
    }
  }, []);

  // 下载模板
  const downloadTemplate = useCallback(async () => {
    try {
      const response = await getMountainImportTemplateInfo();

      if (response.errCode === 0 && response.data) {
        const { downloadUrl, filename, buffer } = response.data;

        if (buffer) {
          // 如果有base64数据，直接下载
          const byteCharacters = atob(buffer);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);
          const blob = new Blob([byteArray], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });

          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = filename || '山塬导入模板.xlsx';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(link.href);
        } else if (downloadUrl) {
          // 使用下载链接
          const link = document.createElement('a');
          link.href = downloadUrl;
          link.download = filename || '山塬导入模板.xlsx';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }

        message.success('模板下载成功');
      } else {
        message.error(response.msg || '获取模板下载链接失败');
      }
    } catch (error: any) {
      console.error('下载模板失败:', error);
      const errorMsg = error?.response?.data?.msg || '下载模板失败';
      message.error(errorMsg);
    }
  }, []);

  // 预览Excel
  const previewExcel = useCallback(async (file: File) => {
    setBatchLoading(true);
    try {
      const response = await previewMountainExcel(file);

      if (response.errCode === 0) {
        return { success: true, data: response.data };
      } else {
        message.error(response.msg || 'Excel文件预览失败');
        return { success: false, message: response.msg };
      }
    } catch (error: any) {
      console.error('预览Excel失败:', error);
      const errorMsg = error?.response?.data?.msg || 'Excel文件预览失败';
      message.error(errorMsg);
      return { success: false, message: errorMsg };
    } finally {
      setBatchLoading(false);
    }
  }, []);

  // 导入Excel
  const importExcel = useCallback(async (file: File) => {
    setBatchLoading(true);
    try {
      const response = await importMountainExcelFile(file);

      if (response.errCode === 0) {
        message.success('导入成功');
        return { success: true, data: response.data };
      } else {
        message.error(response.msg || 'Excel文件导入失败');
        return { success: false, message: response.msg };
      }
    } catch (error: any) {
      console.error('导入Excel失败:', error);
      const errorMsg = error?.response?.data?.msg || 'Excel文件导入失败';
      message.error(errorMsg);
      return { success: false, message: errorMsg };
    } finally {
      setBatchLoading(false);
    }
  }, []);

  // 批量导入山塬（保留原有JSON导入功能）
  const batchImportElements = useCallback(
    async (elements: API.CreateMountainParams[]) => {
      setBatchLoading(true);
      try {
        const response = await batchImportMountains({ elements });

        if (response.errCode === 0) {
          message.success('批量导入成功');
          return { success: true };
        } else {
          message.error(response.msg || '批量导入失败');
          return { success: false, message: response.msg };
        }
      } catch (error: any) {
        console.error('批量导入失败:', error);
        const errorMsg = error?.response?.data?.msg || '批量导入失败';
        message.error(errorMsg);
        return { success: false, message: errorMsg };
      } finally {
        setBatchLoading(false);
      }
    },
    [],
  );

  return {
    // 加载状态
    operationLoading,
    batchLoading,

    // CRUD操作
    createElement,
    updateElement,
    deleteElement,
    batchDeleteElements,

    // 导入导出操作
    downloadTemplate,
    previewExcel,
    importExcel,
    batchImportElements,
  };
};
