/**
 * @file 数据管理布局组件
 * @description 通用的数据管理页面布局，包含左侧区域选择器和右侧内容区域
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */
import { Card, Col, Row } from 'antd';
import React from 'react';
import RegionTreeSelector from './RegionTreeSelector';
import './index.less';

export interface DataManagementLayoutProps {
  /** 左侧区域选择器的宽度，默认为6 */
  selectorSpan?: number;
  /** 右侧内容区域的宽度，默认为18 */
  contentSpan?: number;
  /** 当前选中的区域ID */
  selectedRegionId?: number;
  /** 区域选择变化回调 */
  onRegionChange?: (regionId?: number) => void;
  /** 右侧内容区域 */
  children: React.ReactNode;
  /** 是否显示区域选择器，默认为true */
  showRegionSelector?: boolean;
  /** 区域选择器标题，默认为"区域选择" */
  selectorTitle?: string;
  /** 内容区域标题 */
  contentTitle?: string;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const DataManagementLayout: React.FC<DataManagementLayoutProps> = ({
  selectorSpan = 6,
  contentSpan = 18,
  selectedRegionId,
  onRegionChange,
  children,
  showRegionSelector = true,
  selectorTitle = '区域选择',
  contentTitle,
  className,
  style,
}) => {
  // 如果不显示区域选择器，内容区域占满整行
  const actualContentSpan = showRegionSelector ? contentSpan : 24;
  const actualSelectorSpan = showRegionSelector ? selectorSpan : 0;

  return (
    <div className={`data-management-layout ${className || ''}`} style={style}>
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 左侧区域选择器 */}
        {showRegionSelector && (
          <Col span={actualSelectorSpan}>
            <Card
              title={selectorTitle}
              className="region-selector-card"
              bodyStyle={{ padding: '12px' }}
            >
              <RegionTreeSelector
                selectedRegionId={selectedRegionId}
                onRegionChange={onRegionChange}
              />
            </Card>
          </Col>
        )}

        {/* 右侧内容区域 */}
        <Col span={actualContentSpan}>
          <Card
            title={contentTitle}
            className="content-card"
            bodyStyle={{ padding: '16px' }}
          >
            {children}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataManagementLayout;
