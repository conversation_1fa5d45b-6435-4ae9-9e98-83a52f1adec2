export default [
  // 公共网站路由（不使用layout）
  {
    path: '/',
    component: '@/pages/Home',
    layout: false,
  },
  {
    path: '/mountain',
    component: '@/pages/Mountain',
    layout: false,
  },
  {
    path: '/water-system',
    component: '@/pages/WaterSystem',
    layout: false,
  },
  {
    path: '/historical-element',
    component: '@/pages/HistoricalElement',
    layout: false,
  },
  {
    path: '/digital',
    component: '@/pages/Digital',
    layout: false,
  },
  {
    path: '/detail/:type/:id',
    component: '@/pages/Detail',
    layout: false,
  },
  // 管理端登录页（不使用layout）
  {
    path: '/admin/login',
    component: '@/pages/Admin/Login',
    layout: false,
  },
  // 403无权限页面
  {
    path: '/403',
    component: '@/pages/403',
    layout: false,
  },
  // 管理端路由（使用layout）
  {
    path: '/admin',
    access: 'isLoggedIn',
    flatMenu: true,
    routes: [
      {
        path: '/admin',
        redirect: '/admin/dashboard',
      },
      {
        path: '/admin/dashboard',
        name: '仪表盘',
        icon: 'DashboardOutlined',
        component: '@/pages/Admin/Dashboard',
        access: 'canView',
      },
      {
        path: '/admin/mountain',
        name: '山塬管理',
        icon: 'EnvironmentOutlined',
        component: '@/pages/Admin/Mountain',
        access: 'canEdit',
      },
      {
        path: '/admin/water-system',
        name: '水系管理',
        icon: 'GlobalOutlined',
        component: '@/pages/Admin/WaterSystem',
        access: 'canEdit',
      },
      {
        path: '/admin/historical-element',
        name: '历史要素管理',
        icon: 'HistoryOutlined',
        component: '@/pages/Admin/HistoricalElement',
        access: 'canEdit',
      },
      {
        path: '/admin/element-relation',
        name: '要素关联管理',
        icon: 'ShareAltOutlined',
        component: '@/pages/Admin/ElementRelation',
        access: 'canEdit',
      },
      {
        path: '/admin/dictionary',
        name: '字典管理',
        icon: 'BookOutlined',
        component: '@/pages/Admin/Dictionary',
        access: 'canManageDictionary',
      },
      {
        path: '/admin/upload',
        name: '资源管理',
        icon: 'CloudUploadOutlined',
        component: '@/pages/Admin/Upload',
        access: 'canManageResources',
      },
      {
        path: '/admin/user',
        name: '用户管理',
        icon: 'UserOutlined',
        component: '@/pages/Admin/User',
        access: 'canManageUsers',
      },
    ],
  },
] as IBestAFSRoute[];
