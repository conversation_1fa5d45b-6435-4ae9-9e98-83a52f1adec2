import { request } from '@umijs/max';

/**
 * 创建山塬
 */
export async function createMountain(
  params: API.CreateMountainParams,
): Promise<API.ResType<API.Mountain>> {
  return request('/admin/mountain', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新山塬
 */
export async function updateMountain(
  id: number,
  params: API.UpdateMountainParams,
): Promise<API.ResType<API.Mountain>> {
  return request(`/admin/mountain/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除山塬
 */
export async function deleteMountain(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/mountain/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取山塬详情
 */
export async function getMountainDetail(
  id: number,
): Promise<API.ResType<API.Mountain>> {
  return request(`/admin/mountain/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取山塬列表
 */
export async function getMountainList(
  params?: API.GetMountainListParams,
): Promise<API.ResType<API.MountainListResponse>> {
  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize)
    queryParams.append('pageSize', params.pageSize.toString());
  if (params?.keyword) queryParams.append('keyword', params.keyword);
  if (params?.regionId)
    queryParams.append('regionId', params.regionId.toString());
  if (params?.typeId) queryParams.append('typeId', params.typeId.toString());

  const url = `/admin/mountain${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 批量删除山塬
 */
export async function batchDeleteMountains(
  ids: number[],
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/mountain/batch-delete', {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 获取Excel导入模板下载信息
 */
export async function getMountainImportTemplateInfo(): Promise<
  API.ResType<API.TemplateDownloadResponse>
> {
  return request('/admin/mountain/template/download', {
    method: 'GET',
  });
}

/**
 * 预览Excel文件
 */
export async function previewMountainExcel(
  file: File,
): Promise<API.ResType<any[]>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/mountain/import/preview', {
    method: 'POST',
    data: formData,
  });
}

/**
 * Excel文件导入
 */
export async function importMountainExcelFile(
  file: File,
): Promise<API.ResType<API.ExcelImportResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/mountain/import/execute', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 批量导入山塬（保留原有JSON导入功能）
 */
export async function batchImportMountains(
  params: API.BatchImportParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/mountain/batch-import', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取山塬统计
 */
export async function getMountainStatistics(params?: {
  regionId?: number;
}): Promise<API.ResType<API.MountainStatistics>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId)
    queryParams.append('regionId', params.regionId.toString());

  const url = `/admin/mountain/statistics/overview${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 根据类型获取山塬
 */
export async function getMountainsByType(
  typeId: number,
): Promise<API.ResType<API.Mountain[]>> {
  return request(`/admin/mountain/by-type/${typeId}`, {
    method: 'GET',
  });
}

/**
 * 根据区域获取山塬
 */
export async function getMountainsByRegion(
  regionId: number,
): Promise<API.ResType<API.Mountain[]>> {
  return request(`/admin/mountain/by-region/${regionId}`, {
    method: 'GET',
  });
}

/**
 * 根据高度范围查询山塬
 */
export async function getMountainsByHeight(params: {
  minHeight?: number;
  maxHeight?: number;
}): Promise<API.ResType<API.Mountain[]>> {
  const queryParams = new URLSearchParams();
  if (params.minHeight)
    queryParams.append('minHeight', params.minHeight.toString());
  if (params.maxHeight)
    queryParams.append('maxHeight', params.maxHeight.toString());

  const url = `/admin/mountain/by-height${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取所有山塬（用于下拉选择）
 */
export async function getAllMountains(): Promise<API.ResType<API.Mountain[]>> {
  return request('/openapi/mountain/all', {
    method: 'GET',
  });
}

// ==================== 公开接口（门户） ====================

export async function getPublicMountainList(
  params?: API.GetMountainListParams,
): Promise<API.ResType<API.MountainListResponse>> {
  const query = new URLSearchParams();
  if (params?.page) query.append('page', String(params.page));
  if (params?.pageSize) query.append('pageSize', String(params.pageSize));
  if (params?.keyword) query.append('keyword', params.keyword);
  if (params?.regionId) query.append('regionId', String(params.regionId));
  if (params?.typeId) query.append('typeId', String(params.typeId));
  const url = `/openapi/mountain/list${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getPublicMountainDetail(
  id: number,
): Promise<API.ResType<API.Mountain>> {
  return request(`/openapi/mountain/${id}`, { method: 'GET' });
}

export async function getPublicMountainPhotos(
  id: number,
): Promise<API.ResType<Array<{ id: number; name: string; url: string }>>> {
  return request(`/openapi/mountain/${id}/photos`, { method: 'GET' });
}

/**
 * 上传山塬照片
 */
export async function uploadMountainPhoto(
  mountainId: number,
  file: File,
): Promise<API.ResType<{ id: number; name: string; url: string }>> {
  const formData = new FormData();
  formData.append('file', file);

  return request(`/admin/mountain/${mountainId}/photo`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 删除山塬照片
 */
export async function deleteMountainPhoto(
  mountainId: number,
  photoId: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/mountain/${mountainId}/photo/${photoId}`, {
    method: 'DELETE',
  });
}
