// 背景图配置文件
export const BACKGROUND_CONFIG = {
  // 首页Hero区域背景图
  hero: {
    primary:
      'https://images.unsplash.com/photo-1725933014999-e70ae6e57375?fm=jpg&q=80&w=1920&h=1080&fit=crop',
    fallback:
      'https://images.unsplash.com/photo-1665849189343-c302695244a4?fm=jpg&q=80&w=1920&h=1080&fit=crop',
    description: '西安城市天际线夕阳景观',
    overlay:
      'linear-gradient(135deg, rgba(102, 126, 234, 0.6) 0%, rgba(118, 75, 162, 0.6) 50%, rgba(240, 147, 251, 0.6) 100%)',
  },

  // 山塬页面背景图
  mountain: {
    primary:
      'https://images.unsplash.com/photo-1567266565446-d9c40ccf59a4?fm=jpg&q=80&w=1920&h=800&fit=crop',
    fallback:
      'https://images.unsplash.com/photo-1491497895121-1334fc14d8c9?fm=jpg&q=80&w=1920&h=800&fit=crop',
    description: '灰色岩石地貌，层次分明',
    overlay:
      'linear-gradient(135deg, rgba(139, 69, 19, 0.7) 0%, rgba(160, 82, 45, 0.7) 100%)',
  },

  // 水系页面背景图
  water: {
    primary:
      'https://images.unsplash.com/photo-1603296325966-6b48b633ada3?fm=jpg&q=80&w=1920&h=800&fit=crop',
    fallback:
      'https://images.unsplash.com/photo-1677922069750-944be2b9ad20?fm=jpg&q=80&w=1920&h=800&fit=crop',
    description: '绿色河流穿越树林',
    overlay:
      'linear-gradient(135deg, rgba(30, 144, 255, 0.7) 0%, rgba(0, 191, 255, 0.7) 100%)',
  },

  // 历史要素页面背景图
  history: {
    primary:
      'https://images.unsplash.com/photo-1517454376155-60f890ea84be?fm=jpg&q=80&w=1920&h=800&fit=crop',
    fallback:
      'https://images.unsplash.com/photo-1615632111211-075c0ddcb8c7?fm=jpg&q=80&w=1920&h=800&fit=crop',
    description: '中国传统塔楼建筑',
    overlay:
      'linear-gradient(135deg, rgba(184, 134, 11, 0.7) 0%, rgba(146, 64, 14, 0.7) 100%)',
  },

  // 数字化页面背景图
  digital: {
    primary:
      'https://images.unsplash.com/photo-1509195070461-b99ef33ceb67?fm=jpg&q=80&w=1920&h=800&fit=crop',
    fallback:
      'https://images.unsplash.com/photo-1521069954693-0aaad7b1cd03?fm=jpg&q=80&w=1920&h=800&fit=crop',
    description: '长城鸟瞰图',
    overlay:
      'linear-gradient(135deg, rgba(16, 185, 129, 0.7) 0%, rgba(5, 150, 105, 0.7) 100%)',
  },
};

// 背景图类型
export type BackgroundType = keyof typeof BACKGROUND_CONFIG;

// 获取背景图URL
export const getBackgroundUrl = (
  type: BackgroundType,
  useFallback = false,
): string => {
  const config = BACKGROUND_CONFIG[type];
  return useFallback ? config.fallback : config.primary;
};

// 获取背景图样式
export const getBackgroundStyle = (
  type: BackgroundType,
  useFallback = false,
) => {
  const config = BACKGROUND_CONFIG[type];
  const imageUrl = useFallback ? config.fallback : config.primary;

  return {
    backgroundImage: `${config.overlay}, url(${imageUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll',
  };
};

// 预加载背景图
export const preloadBackground = (type: BackgroundType): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const config = BACKGROUND_CONFIG[type];

    img.onload = () => resolve();
    img.onerror = () => {
      // 如果主图片加载失败，尝试加载备用图片
      const fallbackImg = new Image();
      fallbackImg.onload = () => resolve();
      fallbackImg.onerror = reject;
      fallbackImg.src = config.fallback;
    };

    img.src = config.primary;
  });
};

// 批量预加载所有背景图
export const preloadAllBackgrounds = async (): Promise<void> => {
  const types: BackgroundType[] = [
    'hero',
    'mountain',
    'water',
    'history',
    'digital',
  ];

  try {
    await Promise.all(types.map((type) => preloadBackground(type)));
    console.log('✅ 所有背景图预加载完成');
  } catch (error) {
    console.warn('⚠️ 部分背景图预加载失败:', error);
  }
};
