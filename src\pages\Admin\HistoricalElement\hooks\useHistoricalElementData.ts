import {
  getHistoricalElementList,
  getHistoricalElementStatistics,
  getHistoricalElementTimeline,
} from '@/services/historicalElement';
import { message } from 'antd';
import { useCallback, useState } from 'react';

export interface UseHistoricalElementDataReturn {
  // 数据状态
  data: API.HistoricalElement[];
  statistics: API.HistoricalElementStatistics | null;
  timelineData: API.TimelineData[];

  // 加载状态
  loading: boolean;
  statisticsLoading: boolean;
  timelineLoading: boolean;

  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };

  // 数据获取函数
  fetchData: (
    page?: number,
    pageSize?: number,
    keyword?: string,
    regionId?: number,
    typeId?: number,
  ) => Promise<void>;
  fetchStatistics: (regionId?: number, typeId?: number) => Promise<void>;
  fetchTimelineData: (regionId?: number) => Promise<void>;

  // 状态更新函数
  setPagination: React.Dispatch<
    React.SetStateAction<{
      current: number;
      pageSize: number;
      total: number;
    }>
  >;
}

export const useHistoricalElementData = (): UseHistoricalElementDataReturn => {
  // 数据状态
  const [data, setData] = useState<API.HistoricalElement[]>([]);
  const [statistics, setStatistics] =
    useState<API.HistoricalElementStatistics | null>(null);
  const [timelineData, setTimelineData] = useState<API.TimelineData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [timelineLoading, setTimelineLoading] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取历史要素列表
  const fetchData = useCallback(
    async (
      page = 1,
      pageSize = 10,
      keyword?: string,
      regionId?: number,
      typeId?: number,
    ) => {
      setLoading(true);
      try {
        const response = await getHistoricalElementList({
          page,
          pageSize,
          keyword: keyword || undefined,
          regionId,
          typeId,
        });

        if (response.errCode === 0 && response.data) {
          // 确保list是数组，如果不是则使用空数组
          const list = Array.isArray(response.data.list)
            ? response.data.list
            : [];
          setData(list);
          setPagination({
            current: response.data.page || 1,
            pageSize: response.data.pageSize || 10,
            total: response.data.total || 0,
          });
        } else {
          console.log('API响应错误，设置空数组');
          message.error(response.msg || '获取历史要素列表失败');
          // 出错时也要确保data是空数组
          setData([]);
        }
      } catch (error: any) {
        console.error('获取历史要素列表失败:', error);

        message.error(error?.response?.data?.msg || '获取历史要素列表失败');
        // 出现异常时确保data是空数组
        console.log('异常情况下设置空数组');
        setData([]);
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取统计数据
  const fetchStatistics = useCallback(
    async (regionId?: number, typeId?: number) => {
      setStatisticsLoading(true);
      try {
        const response = await getHistoricalElementStatistics({
          regionId,
          typeId,
        });

        if (response.errCode === 0 && response.data) {
          setStatistics(response.data);
        } else {
          message.error(response.msg || '获取统计数据失败');
        }
      } catch (error: any) {
        console.error('获取统计数据失败:', error);
        message.error(error?.response?.data?.msg || '获取统计数据失败');
      } finally {
        setStatisticsLoading(false);
      }
    },
    [],
  );

  // 获取时间轴数据
  const fetchTimelineData = useCallback(async (regionId?: number) => {
    setTimelineLoading(true);
    try {
      const response = await getHistoricalElementTimeline({
        regionId,
      });

      if (response.errCode === 0 && response.data) {
        // 确保timelineData是数组
        const timelineData = Array.isArray(response.data) ? response.data : [];
        console.log('设置时间轴数据:', timelineData);
        setTimelineData(timelineData);
      } else {
        console.log('时间轴API响应错误，设置空数组');
        message.error(response.msg || '获取时间轴数据失败');
        // 出错时设置空数组
        setTimelineData([]);
      }
    } catch (error: any) {
      console.log('=== 时间轴API异常调试 ===');
      console.error('获取时间轴数据失败:', error);
      console.log('=== 时间轴异常调试结束 ===');
      message.error(error?.response?.data?.msg || '获取时间轴数据失败');
      // 出现异常时设置空数组
      setTimelineData([]);
    } finally {
      setTimelineLoading(false);
    }
  }, []);

  return {
    // 数据状态
    data,
    statistics,
    timelineData,

    // 加载状态
    loading,
    statisticsLoading,
    timelineLoading,

    // 分页状态
    pagination,

    // 数据获取函数
    fetchData,
    fetchStatistics,
    fetchTimelineData,

    // 状态更新函数
    setPagination,
  };
};
