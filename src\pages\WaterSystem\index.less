/* 水系页面样式 */
.water-system-page {
  background: #f5f5f5;
  min-height: calc(100vh - 300px);
  padding: 40px 0;

  .content-card {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 8%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #1890ff 0%, #36cfc9 100%);
      border-radius: 16px 16px 0 0;
    }
  }

  .region-card {
    height: 600px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 8%);
    border: none;
    overflow: hidden;
    background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #1890ff 0%, #36cfc9 100%);
      z-index: 1;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
      padding: 0 24px;

      .ant-card-head-title {
        font-weight: 600;
        color: #262626;
        font-size: 16px;
        padding: 16px 0;
        display: flex;
        align-items: center;

        &::before {
          content: '💧';
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }

    .ant-card-body {
      height: calc(100% - 73px);
      overflow-y: auto;
      padding: 0;
      background: linear-gradient(135deg, #fff 0%, #fafafa 100%);

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f5f5;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(180deg, #1890ff 0%, #36cfc9 100%);
        border-radius: 3px;

        &:hover {
          background: linear-gradient(180deg, #096dd9 0%, #13c2c2 100%);
        }
      }
    }
  }

  .page-title {
    text-align: center;
    margin-bottom: 16px;
    color: #333;
    font-size: 28px;
    font-weight: 600;
  }

  .page-description {
    text-align: center;
    font-size: 16px;
    margin-bottom: 40px;
    color: #666;
    line-height: 1.6;
  }

  .list-container {
    .ant-list-item {
      margin-bottom: 24px; /* 添加行间距 */
      height: 100%; /* 确保列表项高度一致 */
    }

    .ant-list-grid .ant-col {
      margin-bottom: 24px; /* 网格布局的行间距 */
      display: flex; /* 确保列也是 flex 布局 */

      > div {
        width: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .water-card {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 8%) !important;
    transition: all 0.3s ease !important;
    border: none !important;
    height: 100% !important;
    width: 100% !important;
    display: flex !important;
    flex-direction: column !important;

    .water-cover {
      height: 200px;
      background: linear-gradient(135deg, #91d5ff 0%, #b37feb 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      flex-shrink: 0;

      .water-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 90%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: #1890ff;
      }
    }

    .ant-card-body {
      padding: 16px !important;
      flex: 1 !important;
      display: flex !important;
      flex-direction: column !important;
    }

    .card-content {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .card-title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .card-code {
        font-size: 12px;
        background: #e6f4ff;
        color: #1677ff;
        padding: 2px 8px;
        border-radius: 4px;
        font-weight: 500;
      }
    }

    .card-info {
      margin-bottom: 6px;
      color: #666;
      font-size: 13px;

      .info-label {
        color: #999;
      }
    }

    .card-description {
      margin-bottom: 16px;
      color: #666;
      font-size: 13px;
      display: box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      overflow: hidden;
      line-height: 1.5;
      flex: 1;
      min-height: 60px; /* 确保至少3行的高度 */
    }

    .card-action {
      text-align: center;
      margin-top: auto;
      flex-shrink: 0;
      padding-top: 12px;

      .detail-button {
        border-radius: 6px;
        font-size: 12px;
        height: 28px;
        padding: 0 16px;
        background: linear-gradient(135deg, #1890ff 0%, #91d5ff 100%);
        border: none;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 20%);
        transition: all 0.3s ease;

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #b37feb 100%);
          box-shadow: 0 4px 8px rgba(24, 144, 255, 30%);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}
