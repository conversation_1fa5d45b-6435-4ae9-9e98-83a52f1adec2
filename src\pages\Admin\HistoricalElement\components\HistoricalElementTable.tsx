import DictSelect from '@/components/DictSelect';
import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Space, Table, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import React from 'react';

export interface HistoricalElementTableProps {
  data: API.HistoricalElement[];
  loading: boolean;
  selectedRowKeys: React.Key[];
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onSelectChange: (selectedRowKeys: React.Key[]) => void;
  onView: (record: API.HistoricalElement) => void;
  onEdit: (record: API.HistoricalElement) => void;
  onDelete: (id: number) => void;
  onPaginationChange: (page: number, pageSize: number) => void;
  onShowSizeChange: (current: number, size: number) => void;
}

export const HistoricalElementTable: React.FC<HistoricalElementTableProps> = ({
  data,
  loading,
  selectedRowKeys,
  pagination,
  onSelectChange,
  onView,
  onEdit,
  onDelete,
  onPaginationChange,
  onShowSizeChange,
}) => {
  // 表格列定义
  const columns: ColumnsType<API.HistoricalElement> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '历史要素名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left',
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      ),
    },
    {
      title: '类型',
      dataIndex: 'typeDictId',
      key: 'typeDictId',
      width: 100,
      render: (_: any, record: API.HistoricalElement) => {
        return record.typeDictId ? (
          <Tag color="green">
            <DictSelect.DictDisplay type="type" id={record.typeDictId} />
          </Tag>
        ) : (
          <Tag color="default">未知类型</Tag>
        );
      },
    },
    {
      title: '建造时间',
      dataIndex: 'constructionTime',
      key: 'constructionTime',
      width: 120,
      render: (time: string) => (
        <span style={{ fontWeight: 500, color: '#1890ff' }}>
          {time ? dayjs(time).format('YYYY年') : '-'}
        </span>
      ),
    },
    {
      title: '所属区域',
      dataIndex: 'regionDictId',
      key: 'regionDictId',
      width: 120,
      render: (_: any, record: API.HistoricalElement) => {
        return record.regionDictId ? (
          <Tag color="orange">
            <DictSelect.DictDisplay type="region" id={record.regionDictId} />
          </Tag>
        ) : (
          <Tag color="default">未知区域</Tag>
        );
      },
    },
    {
      title: '经度',
      dataIndex: 'constructionLongitude',
      key: 'constructionLongitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>{value || '-'}</span>
      ),
    },
    {
      title: '纬度',
      dataIndex: 'constructionLatitude',
      key: 'constructionLatitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>{value || '-'}</span>
      ),
    },
    {
      title: '历史记载',
      dataIndex: 'historicalRecords',
      key: 'historicalRecords',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <span>{text || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {text ? new Date(text).toLocaleString() : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_: any, record: API.HistoricalElement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onView(record);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(record);
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个历史要素吗？"
              description="删除后将无法恢复，请谨慎操作。"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
    getCheckboxProps: (record: API.HistoricalElement) => ({
      disabled: false,
      name: record.name,
    }),
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={rowSelection}
      scroll={{ x: 1600, y: 'calc(100vh - 400px)' }}
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100'],
        onChange: onPaginationChange,
        onShowSizeChange,
      }}
      size="small"
      bordered
      style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        overflow: 'hidden',
      }}
    />
  );
};
