import RegionTreeSelector from '@/components/DataManagementLayout/RegionTreeSelector';
import PageHeader from '@/components/PageHeader';
import PublicLayout from '@/components/PublicLayout';
import { getPublicMountainList } from '@/services/mountain';
import { history } from '@umijs/max';
import { Button, Card, Col, List, message, Row, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

// const { Title, Paragraph } = Typography;

const MountainPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.Mountain[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);
  const [selectedRegionId, setSelectedRegionId] = useState<
    number | undefined
  >();

  const fetchList = async (
    p = page,
    ps = pageSize,
    regionId = selectedRegionId,
  ) => {
    setLoading(true);
    try {
      const params: any = { page: p, pageSize: ps };
      if (regionId) {
        params.regionId = regionId;
      }
      const res = await getPublicMountainList(params);
      if (res.errCode === 0 && res.data) {
        setData(res.data.list || []);
        setTotal(res.data.total || 0);
        setPage(res.data.page || p);
        setPageSize(res.data.pageSize || ps);
      } else {
        message.error(res.msg || '加载失败');
      }
    } catch (e: any) {
      message.error(e?.message || '加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理区域选择变化
  const handleRegionChange = (regionId?: number) => {
    setSelectedRegionId(regionId);
    setPage(1); // 重置到第一页
    fetchList(1, pageSize, regionId);
  };

  useEffect(() => {
    fetchList(1, 12);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // const handleDetailClick = (id: number) => {
  //   history.push(`/detail/mountain/${id}`);
  // };

  // const getRegionName = (regionId: number) => {
  //   // TODO: 从API获取区域名称
  //   console.log(regionId);
  //   return '未知区域';
  // };

  return (
    <PublicLayout>
      {/* 页面头部Banner */}
      <PageHeader
        title="关中地区山塬"
        description="山塬是关中地区重要的地理要素，承载着深厚的历史文化内涵。"
        backgroundType="mountain"
        height={300}
      />

      <div className="mountain-page">
        <div className="content-card">
          <Row gutter={24}>
            {/* 左侧区域树 */}
            <Col xs={24} sm={6} md={6} lg={5} xl={4}>
              <Card
                title="区域筛选"
                className="region-card"
                styles={{ body: { padding: '5px' } }}
              >
                <RegionTreeSelector
                  selectedRegionId={selectedRegionId}
                  onRegionChange={handleRegionChange}
                />
              </Card>
            </Col>

            {/* 右侧内容列表 */}
            <Col xs={24} sm={18} md={18} lg={19} xl={20}>
              <div className="list-container">
                <List
                  loading={loading}
                  grid={{
                    gutter: 24,
                    xs: 1,
                    sm: 2,
                    md: 2,
                    lg: 3,
                    xl: 3,
                    xxl: 3,
                  }}
                  dataSource={data}
                  locale={{ emptyText: '暂无数据' }}
                  pagination={{
                    current: page,
                    pageSize,
                    total,
                    onChange: (cp, cps) => fetchList(cp, cps),
                    showSizeChanger: true,
                  }}
                  renderItem={(item) => (
                    <List.Item>
                      <Card
                        hoverable
                        className="mountain-card"
                        onClick={() =>
                          history.push(`/detail/mountain/${item.id}`)
                        }
                        cover={
                          <div className="mountain-cover">
                            <div className="mountain-icon">🏔️</div>
                          </div>
                        }
                      >
                        <div className="card-content">
                          <div className="card-header">
                            <Typography.Text strong className="card-title">
                              {item.name}
                            </Typography.Text>
                            <span className="card-code">{item.code}</span>
                          </div>
                          <div className="card-info">
                            <span className="info-label">所属区域：</span>
                            {item.regionDictId || '未知区域'}
                          </div>
                          <div className="card-info">
                            <span className="info-label">海拔：</span>
                            {item.height ? `${item.height}米` : '-'}
                          </div>
                          <div className="card-description">
                            {item.historicalRecords || '暂无历史记载信息'}
                          </div>
                          <div className="card-action">
                            <Button
                              type="primary"
                              size="small"
                              className="detail-button"
                              onClick={(e) => {
                                e.stopPropagation();
                                history.push(`/detail/mountain/${item.id}`);
                              }}
                            >
                              查看详情
                            </Button>
                          </div>
                        </div>
                      </Card>
                    </List.Item>
                  )}
                />
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </PublicLayout>
  );
};

export default MountainPage;
