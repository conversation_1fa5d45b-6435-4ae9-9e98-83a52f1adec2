import { request } from '@umijs/max';

// 门户公开服务

export interface PortalOverviewData {
  statistics: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
  };
  regionDistribution: Array<{
    region: string;
    regionId: number;
    mountainCount: number;
    waterSystemCount: number;
    historicalElementCount: number;
    total: number;
  }>;
  recentData: {
    mountains: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
    waterSystems: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
    historicalElements: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
  };
}

export interface MapMarkerItem {
  id: number;
  type: 'mountain' | 'waterSystem' | 'historicalElement';
  name: string;
  longitude: number;
  latitude: number;
  thumbnailUrl?: string;
  summary?: string;
}

export async function getPortalOverview(params?: {
  regionId?: number;
}): Promise<API.ResType<PortalOverviewData>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/portal/overview${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getPortalMapMarkers(params?: {
  types?: Array<'mountain' | 'waterSystem' | 'historicalElement'>;
  regionId?: number;
}): Promise<API.ResType<MapMarkerItem[]>> {
  const query = new URLSearchParams();
  if (params?.types?.length) query.append('types', params.types.join(','));
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/portal/map-markers${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

// ==================== 数字化统计相关接口 ====================

/**
 * 基础统计数据类型定义
 */
export interface BasicStatisticsData {
  counts: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
    user: number;
    typeDict: number;
    regionDict: number;
    relationshipDict: number;
  };
  regionStats: Array<{
    region: string;
    regionId: number;
    mountainCount: number;
    waterSystemCount: number;
    historicalElementCount: number;
    total: number;
  }>;
  timelineData: Array<{
    year: number;
    elements: Array<{
      id: number;
      name: string;
      type: string;
    }>;
  }>;
}

/**
 * 区域分布统计数据类型定义
 */
export interface RegionDistributionData {
  region: string;
  regionId: number;
  mountainCount: number;
  waterSystemCount: number;
  historicalElementCount: number;
  total: number;
}

/**
 * 时间轴数据类型定义
 */
export interface TimelineStatisticsData {
  year: number;
  elements: Array<{
    id: number;
    name: string;
    type: string;
  }>;
}

/**
 * 数据概览类型定义
 */
export interface OverviewStatisticsData {
  totalCounts: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
    user: number;
    typeDict: number;
    regionDict: number;
    relationshipDict: number;
  };
  regionStats: RegionDistributionData[];
  timelineData: TimelineStatisticsData[];
}

/**
 * 获取基础统计数据
 */
export async function getBasicStatistics(params?: {
  regionId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<API.ResType<BasicStatisticsData>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  if (params?.startTime) query.append('startTime', params.startTime);
  if (params?.endTime) query.append('endTime', params.endTime);
  const url = `/openapi/statistics/basic${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

/**
 * 获取综合统计报告
 */
export async function getComprehensiveStatistics(params?: {
  regionId?: number;
}): Promise<API.ResType<any>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/comprehensive${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

/**
 * 获取时间轴数据
 */
export async function getTimelineStatistics(params?: {
  regionId?: number;
}): Promise<API.ResType<TimelineStatisticsData[]>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/timeline${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

/**
 * 获取区域分布统计
 */
export async function getRegionDistributionStatistics(): Promise<
  API.ResType<RegionDistributionData[]>
> {
  return request('/openapi/statistics/region-distribution', { method: 'GET' });
}

/**
 * 获取数据概览
 */
export async function getOverviewStatistics(params?: {
  regionId?: number;
}): Promise<API.ResType<OverviewStatisticsData>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/overview${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

/**
 * @deprecated 使用 getBasicStatistics 替代
 */
export async function getStatisticsOverview(params?: {
  regionId?: number;
}): Promise<
  API.ResType<{
    counts: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
    };
    regionStats: Array<{
      region: string;
      regionId: number;
      mountainCount: number;
      waterSystemCount: number;
      historicalElementCount: number;
      total: number;
    }>;
  }>
> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/overview${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

/**
 * @deprecated 使用 getTimelineStatistics 替代
 */
export async function getStatisticsTimeline(params?: {
  regionId?: number;
}): Promise<API.ResType<API.TimelineData[]>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/timeline${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getStatisticsGrowthTrend(params?: {
  period?: 'week' | 'month' | 'year';
}): Promise<
  API.ResType<{
    mountains: Array<{ date: string; count: number }>;
    waterSystems: Array<{ date: string; count: number }>;
    historicalElements: Array<{ date: string; count: number }>;
  }>
> {
  const query = new URLSearchParams();
  if (params?.period) query.append('period', params.period);
  const url = `/openapi/statistics/growth-trend${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function openSearch(params: {
  keyword: string;
  type?: 'mountain' | 'waterSystem' | 'historicalElement' | 'all';
  page?: number;
  pageSize?: number;
}): Promise<
  API.ResType<{
    list: Array<
      | (API.Mountain & { type: 'mountain' })
      | (API.WaterSystem & { type: 'waterSystem' })
      | (API.HistoricalElement & { type: 'historicalElement' })
    >;
    total: number;
    page: number;
    pageSize: number;
  }>
> {
  const query = new URLSearchParams();
  query.append('keyword', params.keyword);
  if (params.type) query.append('type', params.type);
  if (params.page) query.append('page', String(params.page));
  if (params.pageSize) query.append('pageSize', String(params.pageSize));
  const url = `/openapi/search?${query.toString()}`;
  return request(url, { method: 'GET' });
}
