# 详情页关系网络图实现说明

## 功能概述

在详情页下方增加了一个关系网络图组件，用于可视化展示当前要素与其他要素之间的关联关系。

## 实现内容

### 1. 组件引入
- 复用了管理端的 `NetworkGraph` 组件
- 导入路径：`@/components/NetworkGraph`

### 2. 数据转换功能
实现了 `convertRelationsToNetworkData` 函数，将关联关系数据转换为网络图所需的数据格式：

#### 输入数据
- `relations`: API.ElementRelation[] - 关联关系数组
- `currentElement`: 当前要素的详情数据

#### 输出数据
- `nodes`: 网络图节点数组，包含当前要素和所有关联要素
- `links`: 网络图连线数组，表示要素之间的关系
- `categories`: 要素类型分类数组

### 3. 节点配置
- **当前要素节点**: 作为中心节点，尺寸较大 (size: 20)
- **关联要素节点**: 根据要素类型设置不同颜色和尺寸 (size: 15)
- **颜色映射**:
  - 山塬: #8B4513 (棕色)
  - 水系: #4169E1 (蓝色)  
  - 历史要素: #DC143C (红色)
  - 类型字典: #32CD32 (绿色)
  - 区域字典: #FFD700 (金色)

### 4. 连线配置
- **关系类型颜色映射**:
  - 选址关联: #4169E1 (蓝色)
  - 视线关联: #9932CC (紫色)
  - 历史关联: #DC143C (红色)
  - 功能关联: #32CD32 (绿色)
  - 其他关联: #808080 (灰色)
- **连线标签**: 显示关系的方向或词条信息

### 5. 布局和样式
- 网络图高度: 600px
- 布局: 占据整行宽度，提供更好的可视化空间
- 响应式布局: 在所有屏幕尺寸下都占满整行
- 卡片样式: 与其他详情卡片保持一致

### 6. 显示逻辑
- 只有当存在关联关系数据且成功转换为网络图数据时才显示
- 加载状态与关联关系数据加载状态同步
- 无数据时不显示网络图组件

## 技术实现

### 数据流程
1. 详情页加载时同时获取要素详情和关联关系数据
2. 在关联关系数据加载完成后，调用转换函数生成网络图数据
3. 将网络图数据传递给 NetworkGraph 组件进行渲染

### 关键函数
- `loadRelationsWithDetail()`: 加载关联关系并生成网络图数据
- `convertRelationsToNetworkData()`: 数据转换核心函数
- `getNodeColor()`: 获取节点颜色
- `getLinkColor()`: 获取连线颜色

## 文件修改

### 主要修改文件
- `src/pages/Detail/index.tsx`: 主要实现文件
- `src/pages/Detail/styles.less`: 样式文件

### 新增功能
1. 导入 NetworkGraph 组件
2. 添加 networkData 状态管理
3. 实现数据转换逻辑
4. 添加网络图渲染区域
5. 优化响应式布局

## 使用说明

1. 访问任意要素详情页 (如: `/detail/mountain/1`)
2. 如果该要素存在关联关系，页面下方会显示"关系网络图"卡片
3. 网络图支持缩放、拖拽、节点高亮等交互功能
4. 鼠标悬停可查看节点和连线的详细信息

## 注意事项

- 网络图组件依赖 ECharts 和 echarts-for-react
- 确保后端关联关系接口返回正确的数据格式
- 网络图在移动端会自动适配布局
