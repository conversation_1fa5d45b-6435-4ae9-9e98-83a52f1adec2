import {
  DownloadOutlined,
  FilterOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  InfoCircleOutlined,
  QuestionCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import {
  But<PERSON>,
  Card,
  Col,
  Drawer,
  Form,
  Row,
  Select,
  Slider,
  Space,
  Switch,
  Tooltip,
} from 'antd';
import React, { useCallback, useState } from 'react';
import NetworkGraph from './index';
import NodeDetail from './NodeDetail';

export interface NetworkGraphWithControlsProps {
  data: API.NetworkGraphData | null;
  loading?: boolean;
  title?: string;
  onRefresh?: () => void;
  onExport?: () => void;
  onNodeClick?: (nodeData: any) => void;
  onLinkClick?: (linkData: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

const NetworkGraphWithControls: React.FC<NetworkGraphWithControlsProps> = ({
  data,
  loading = false,
  title = '关系网络图',
  onRefresh,
  onExport,
  onNodeClick,
  onLinkClick,
  style,
  className,
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [filtersVisible, setFiltersVisible] = useState(true);
  const [graphHeight, setGraphHeight] = useState(600);
  const [showLabels, setShowLabels] = useState(true);
  const [nodeSize, setNodeSize] = useState(1);
  const [linkWidth, setLinkWidth] = useState(1);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterRelation, setFilterRelation] = useState<string>('all');
  const [filterDirection, setFilterDirection] = useState<string>('all');
  const [minConnections, setMinConnections] = useState<number>(0);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const [selectedNode, setSelectedNode] = useState<any>(null);

  // 过滤数据
  const filteredData = React.useMemo(() => {
    if (!data) return null;

    let filteredNodes = data.nodes;
    let filteredLinks = data.links;

    // 按类型过滤
    if (filterType !== 'all') {
      filteredNodes = filteredNodes.filter((node) => node.type === filterType);
      const nodeIds = new Set(filteredNodes.map((node) => node.id));
      filteredLinks = filteredLinks.filter(
        (link) => nodeIds.has(link.source) && nodeIds.has(link.target),
      );
    }

    // 按关系过滤
    if (filterRelation !== 'all') {
      filteredLinks = filteredLinks.filter(
        (link) => link.relation === filterRelation,
      );
      const linkNodeIds = new Set([
        ...filteredLinks.map((link) => link.source),
        ...filteredLinks.map((link) => link.target),
      ]);
      filteredNodes = filteredNodes.filter((node) => linkNodeIds.has(node.id));
    }

    // 按方向过滤
    if (filterDirection !== 'all') {
      filteredLinks = filteredLinks.filter(
        (link) => link.direction === filterDirection,
      );
      const linkNodeIds = new Set([
        ...filteredLinks.map((link) => link.source),
        ...filteredLinks.map((link) => link.target),
      ]);
      filteredNodes = filteredNodes.filter((node) => linkNodeIds.has(node.id));
    }

    // 按最小连接数过滤
    if (minConnections > 0) {
      const nodeConnectionCount = new Map<string, number>();
      filteredLinks.forEach((link) => {
        nodeConnectionCount.set(
          link.source,
          (nodeConnectionCount.get(link.source) || 0) + 1,
        );
        nodeConnectionCount.set(
          link.target,
          (nodeConnectionCount.get(link.target) || 0) + 1,
        );
      });

      filteredNodes = filteredNodes.filter(
        (node) => (nodeConnectionCount.get(node.id) || 0) >= minConnections,
      );

      const nodeIds = new Set(filteredNodes.map((node) => node.id));
      filteredLinks = filteredLinks.filter(
        (link) => nodeIds.has(link.source) && nodeIds.has(link.target),
      );
    }

    return {
      ...data,
      nodes: filteredNodes.map((node) => ({
        ...node,
        size: node.size * nodeSize,
      })),
      links: filteredLinks.map((link) => ({
        ...link,
        weight: link.weight * linkWidth,
      })),
    };
  }, [
    data,
    filterType,
    filterRelation,
    filterDirection,
    minConnections,
    nodeSize,
    linkWidth,
  ]);

  // 获取唯一的类型列表
  const nodeTypes = React.useMemo(() => {
    if (!data) return [];
    const types = [...new Set(data.nodes.map((node) => node.type))];
    return types.map((type) => ({
      label:
        type === 'mountain'
          ? '山塬'
          : type === 'water_system'
          ? '水系'
          : type === 'historical_element'
          ? '历史要素'
          : type === 'type_dict'
          ? '类型'
          : type === 'region_dict'
          ? '区域'
          : type,
      value: type,
    }));
  }, [data]);

  // 获取唯一的关系列表
  const relationTypes = React.useMemo(() => {
    if (!data) return [];
    const relations = [...new Set(data.links.map((link) => link.relation))];
    return relations.map((relation) => ({
      label: relation,
      value: relation,
    }));
  }, [data]);

  // 获取唯一的方向列表
  const directionTypes = React.useMemo(() => {
    if (!data) return [];
    const directions = [
      ...new Set(data.links.map((link) => link.direction).filter(Boolean)),
    ];
    return directions.map((direction) => ({
      label: direction,
      value: direction,
    }));
  }, [data]);

  // 切换全屏
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理节点点击 - 只显示详情，不进行筛选
  const handleNodeClick = useCallback(
    (nodeData: any) => {
      // 设置选中的节点并显示详情
      setSelectedNode(nodeData);
      setShowDetail(true);

      // 调用原始的节点点击处理函数
      onNodeClick?.(nodeData);
    },
    [onNodeClick],
  );

  // 处理连线点击 - 快速筛选关系
  const handleLinkClick = useCallback(
    (linkData: any) => {
      // 如果当前已经筛选了该关系，则取消筛选
      if (filterRelation === linkData.relation) {
        setFilterRelation('all');
      } else {
        // 否则筛选该关系
        setFilterRelation(linkData.relation);
      }

      // 调用原始的连线点击处理函数
      onLinkClick?.(linkData);
    },
    [filterRelation, onLinkClick],
  );

  // 重置设置
  const resetSettings = () => {
    setGraphHeight(600);
    setShowLabels(true);
    setNodeSize(1);
    setLinkWidth(1);
    setFilterType('all');
    setFilterRelation('all');
    setFilterDirection('all');
    setMinConnections(0);
  };

  // 重置筛选
  const resetFilters = () => {
    setFilterType('all');
    setFilterRelation('all');
    setFilterDirection('all');
    setMinConnections(0);
  };

  // 筛选栏
  const filtersBar = filtersVisible && (
    <Card size="small" style={{ marginBottom: 8 }}>
      <Row
        gutter={16}
        align="middle"
        style={{ marginBottom: showAdvancedFilters ? 12 : 0 }}
      >
        <Col span={4}>
          <Space>
            <span style={{ fontWeight: 'bold' }}>筛选条件：</span>
            <Tooltip title="提示：点击图中的节点或连线可快速筛选">
              <Button
                type="text"
                size="small"
                style={{ color: '#999', padding: 0 }}
              >
                <QuestionCircleOutlined />
              </Button>
            </Tooltip>
          </Space>
        </Col>
        <Col span={5}>
          <Space>
            <span>要素类型：</span>
            <Select
              value={filterType}
              onChange={setFilterType}
              style={{ width: 100 }}
              size="small"
              options={[{ label: '全部', value: 'all' }, ...nodeTypes]}
            />
          </Space>
        </Col>
        <Col span={5}>
          <Space>
            <span>关系类型：</span>
            <Select
              value={filterRelation}
              onChange={setFilterRelation}
              style={{ width: 100 }}
              size="small"
              options={[{ label: '全部', value: 'all' }, ...relationTypes]}
            />
          </Space>
        </Col>
        <Col span={5}>
          <Space>
            <span>关联方向：</span>
            <Select
              value={filterDirection}
              onChange={setFilterDirection}
              style={{ width: 100 }}
              size="small"
              options={[{ label: '全部', value: 'all' }, ...directionTypes]}
            />
          </Space>
        </Col>
        <Col span={5}>
          <Space>
            <Button
              size="small"
              type={showAdvancedFilters ? 'primary' : 'default'}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            >
              高级筛选
            </Button>
            <Button size="small" onClick={resetFilters}>
              重置
            </Button>
          </Space>
        </Col>
      </Row>

      {showAdvancedFilters && (
        <Row gutter={16} align="middle">
          <Col span={4}>
            <span style={{ color: '#666' }}>高级选项：</span>
          </Col>
          <Col span={8}>
            <Space>
              <span>最小连接数：</span>
              <Select
                value={minConnections}
                onChange={setMinConnections}
                style={{ width: 80 }}
                size="small"
                options={[
                  { label: '不限', value: 0 },
                  { label: '≥1', value: 1 },
                  { label: '≥2', value: 2 },
                  { label: '≥3', value: 3 },
                  { label: '≥5', value: 5 },
                ]}
              />
            </Space>
          </Col>
          <Col span={12}>
            <Space>
              <span style={{ color: '#666', fontSize: '12px' }}>
                当前显示: 节点 {filteredData?.nodes.length || 0} 个 | 关系{' '}
                {filteredData?.links.length || 0} 条
              </span>
              {data && (
                <span style={{ color: '#999', fontSize: '12px' }}>
                  (总计: 节点 {data.nodes.length} 个 | 关系 {data.links.length}{' '}
                  条)
                </span>
              )}
            </Space>
          </Col>
        </Row>
      )}
    </Card>
  );

  // 工具栏
  const toolbar = (
    <Space>
      <Tooltip title={filtersVisible ? '隐藏筛选' : '显示筛选'}>
        <Button
          icon={<FilterOutlined />}
          type={filtersVisible ? 'primary' : 'default'}
          onClick={() => setFiltersVisible(!filtersVisible)}
        />
      </Tooltip>
      <Tooltip title={showDetail ? '隐藏详情' : '显示详情'}>
        <Button
          icon={<InfoCircleOutlined />}
          type={showDetail ? 'primary' : 'default'}
          onClick={() => setShowDetail(!showDetail)}
          disabled={!selectedNode}
        />
      </Tooltip>
      <Tooltip title="刷新数据">
        <Button
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
        />
      </Tooltip>
      <Tooltip title="导出图片">
        <Button icon={<DownloadOutlined />} onClick={onExport} />
      </Tooltip>
      <Tooltip title="设置">
        <Button
          icon={<SettingOutlined />}
          onClick={() => setSettingsVisible(true)}
        />
      </Tooltip>
      <Tooltip title={isFullscreen ? '退出全屏' : '全屏显示'}>
        <Button
          icon={
            isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />
          }
          onClick={toggleFullscreen}
        />
      </Tooltip>
    </Space>
  );

  // 设置面板
  const settingsPanel = (
    <Drawer
      title="图表设置"
      placement="right"
      onClose={() => setSettingsVisible(false)}
      open={settingsVisible}
      width={320}
    >
      <Form layout="vertical">
        <Form.Item label="图表高度">
          <Slider
            min={400}
            max={1200}
            value={graphHeight}
            onChange={setGraphHeight}
            marks={{
              400: '400px',
              600: '600px',
              800: '800px',
              1200: '1200px',
            }}
          />
        </Form.Item>

        <Form.Item label="显示标签">
          <Switch checked={showLabels} onChange={setShowLabels} />
        </Form.Item>

        <Form.Item label="节点大小">
          <Slider
            min={0.5}
            max={2}
            step={0.1}
            value={nodeSize}
            onChange={setNodeSize}
            marks={{
              0.5: '小',
              1: '中',
              2: '大',
            }}
          />
        </Form.Item>

        <Form.Item label="连线粗细">
          <Slider
            min={0.5}
            max={2}
            step={0.1}
            value={linkWidth}
            onChange={setLinkWidth}
            marks={{
              0.5: '细',
              1: '中',
              2: '粗',
            }}
          />
        </Form.Item>

        <Form.Item>
          <Button onClick={resetSettings} block>
            重置设置
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );

  const graphStyle = isFullscreen
    ? {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 1000,
        backgroundColor: '#fff',
      }
    : style;

  return (
    <>
      <Row gutter={16} style={{ height: '100%' }}>
        <Col span={showDetail ? 18 : 24}>
          <Card
            title={title}
            extra={toolbar}
            style={graphStyle}
            className={className}
            styles={{ body: { padding: 0 } }}
          >
            {filtersBar}
            <NetworkGraph
              data={filteredData}
              loading={loading}
              title=""
              height={isFullscreen ? '100vh' : graphHeight}
              onNodeClick={handleNodeClick}
              onLinkClick={handleLinkClick}
            />
          </Card>
        </Col>
        {showDetail && (
          <Col span={6} style={{ height: '100%' }}>
            <NodeDetail nodeData={selectedNode} visible={showDetail} />
          </Col>
        )}
      </Row>
      {settingsPanel}
    </>
  );
};

export default NetworkGraphWithControls;
