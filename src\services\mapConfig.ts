// 地图标记点配置

// 地图标记点类型定义
export interface MapMarkerItem {
  id: number;
  type: 'mountain' | 'waterSystem' | 'historicalElement';
  name: string;
  longitude: number;
  latitude: number;
  elevation?: number;
  summary?: string;
  thumbnailUrl?: string;
  photos?: Array<{
    id: number;
    name: string;
    url: string;
  }>;
}

// 根据类型获取标记点图标
export const getMarkerIcon = (type: MapMarkerItem['type']) => {
  const iconMap = {
    mountain: {
      image: '/images/markers/mountain.png', // 山塬图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
    waterSystem: {
      image: '/images/markers/water.png', // 水系图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
    historicalElement: {
      image: '/images/markers/historical.png', // 历史要素图标
      size: [32, 32] as [number, number],
      offset: [-16, -16] as [number, number],
    },
  };
  return iconMap[type];
};

// 根据类型获取标记点颜色
export const getMarkerColor = (type: MapMarkerItem['type']) => {
  const colorMap = {
    mountain: '#FF4444', // 红色 - 山塬
    waterSystem: '#4285F4', // 蓝色 - 水系
    historicalElement: '#34A853', // 绿色 - 历史要素
  };
  return colorMap[type];
};

// 根据类型获取中文名称
export const getTypeLabel = (type: MapMarkerItem['type']) => {
  const labelMap = {
    mountain: '山塬',
    waterSystem: '水系',
    historicalElement: '历史要素',
  };
  return labelMap[type];
};
