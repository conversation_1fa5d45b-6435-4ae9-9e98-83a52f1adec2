import {
  BarChartOutlined,
  EnvironmentOutlined,
  GlobalOutlined,
  HistoryOutlined,
  HomeOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { history, useLocation } from '@umijs/max';
import { Button } from 'antd';
import React from 'react';

const PublicHeader: React.FC = () => {
  const location = useLocation();

  const navItems = [
    { path: '/', label: '首页', icon: <HomeOutlined /> },
    { path: '/mountain', label: '山塬', icon: <EnvironmentOutlined /> },
    { path: '/water-system', label: '水系', icon: <GlobalOutlined /> },
    {
      path: '/historical-element',
      label: '历史要素',
      icon: <HistoryOutlined />,
    },
    { path: '/digital', label: '数字化', icon: <BarChartOutlined /> },
  ];

  const handleNavClick = (path: string) => {
    history.push(path);
  };

  const handleAdminClick = () => {
    history.push('/admin');
  };

  return (
    <div className="public-header">
      {/* 装饰性背景元素 */}
      <div className="header-decoration">
        <div className="decoration-circle decoration-circle-1"></div>
        <div className="decoration-circle decoration-circle-2"></div>
        <div className="decoration-line"></div>
      </div>

      <div className="public-logo">
        <div className="logo-container">
          <img
            src="/logo.png"
            alt="智慧营建系统"
            style={{ height: '36px', marginRight: '8px' }}
          />
          <div className="logo-text">
            <span className="logo-title">智慧营建</span>
            <span className="logo-subtitle">管理系统</span>
          </div>
        </div>
      </div>

      <nav className="public-nav">
        {navItems.map((item, index) => (
          <a
            key={item.path}
            className={`public-nav-item ${
              location.pathname === item.path ? 'active' : ''
            }`}
            onClick={() => handleNavClick(item.path)}
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <span className="nav-icon">{item.icon}</span>
            <span className="nav-label">{item.label}</span>
            <div className="nav-indicator"></div>
          </a>
        ))}
      </nav>

      <div className="header-actions">
        <Button
          className="admin-entry"
          onClick={handleAdminClick}
          icon={<SettingOutlined />}
        >
          管理端
        </Button>
      </div>
    </div>
  );
};

export default PublicHeader;
