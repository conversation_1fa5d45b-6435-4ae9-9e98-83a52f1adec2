import { useCallback, useState } from 'react';

export interface UseMountainFiltersReturn {
  // 筛选状态
  searchKeyword: string;
  regionFilter: number | undefined;
  typeFilter: number | undefined;

  // 筛选函数
  setSearchKeyword: (keyword: string) => void;
  setRegionFilter: (regionId: number | undefined) => void;
  setTypeFilter: (typeId: number | undefined) => void;
  resetFilters: () => void;

  // 获取当前筛选参数
  getFilterParams: () => {
    keyword?: string;
    regionId?: number;
    typeId?: number;
  };
}

export const useMountainFilters = (): UseMountainFiltersReturn => {
  // 筛选状态
  const [searchKeyword, setSearchKeyword] = useState('');
  const [regionFilter, setRegionFilter] = useState<number | undefined>(
    undefined,
  );
  const [typeFilter, setTypeFilter] = useState<number | undefined>(undefined);

  // 重置筛选条件
  const resetFilters = useCallback(() => {
    setSearchKeyword('');
    setRegionFilter(undefined);
    setTypeFilter(undefined);
  }, []);

  // 获取当前筛选参数
  const getFilterParams = useCallback(() => {
    const params: {
      keyword?: string;
      regionId?: number;
      typeId?: number;
    } = {};

    if (searchKeyword.trim()) {
      params.keyword = searchKeyword.trim();
    }

    if (regionFilter) {
      params.regionId = regionFilter;
    }

    if (typeFilter) {
      params.typeId = typeFilter;
    }

    return params;
  }, [searchKeyword, regionFilter, typeFilter]);

  return {
    // 筛选状态
    searchKeyword,
    regionFilter,
    typeFilter,

    // 筛选函数
    setSearchKeyword,
    setRegionFilter,
    setTypeFilter,
    resetFilters,

    // 获取当前筛选参数
    getFilterParams,
  };
};
