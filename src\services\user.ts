import { request } from '@umijs/max';

// 用户管理相关的API接口

/**
 * 创建用户
 */
export async function createUser(
  params: API.CreateUserParams,
): Promise<API.ResType<API.UserInfo>> {
  return request('/admin/user', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取用户列表
 */
export async function getUserList(
  params?: API.GetUserListParams,
): Promise<API.ResType<API.UserListResponse>> {
  return request('/admin/user', {
    method: 'GET',
    params,
  });
}

/**
 * 获取用户详情
 */
export async function getUserDetail(
  id: number,
): Promise<API.ResType<API.UserInfo>> {
  return request(`/admin/user/${id}`, {
    method: 'GET',
  });
}

/**
 * 更新用户
 */
export async function updateUser(
  id: number,
  params: API.UpdateUserParams,
): Promise<API.ResType<API.UserInfo>> {
  return request(`/admin/user/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除用户
 */
export async function deleteUser(id: number): Promise<API.ResType<null>> {
  return request(`/admin/user/${id}`, {
    method: 'DELETE',
  });
}
