import { request } from '@umijs/max';

// ==================== 字典管理 API ====================

/**
 * 区域字典相关接口
 */

/**
 * 创建区域字典
 */
export async function createRegionDict(
  params: API.CreateRegionDictParams,
): Promise<API.ResType<API.RegionDict>> {
  return request('/admin/region-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新区域字典
 */
export async function updateRegionDict(
  id: number,
  params: API.UpdateRegionDictParams,
): Promise<API.ResType<API.RegionDict>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除区域字典
 */
export async function deleteRegionDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取区域字典详情
 */
export async function getRegionDictDetail(
  id: number,
): Promise<API.ResType<API.RegionDict>> {
  return request(`/admin/region-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取区域字典列表（分页）
 */
export async function getRegionDictList(
  params?: API.GetRegionDictListParams,
): Promise<API.ResType<API.RegionDictListResponse>> {
  return request('/admin/region-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取区域字典树形结构
 */
export async function getRegionDictTree(): Promise<
  API.ResType<API.RegionDict[]>
> {
  return request('/openapi/region-dict/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有区域字典（缓存）
 */
export async function getAllRegionDict(): Promise<
  API.ResType<API.RegionDict[]>
> {
  return request('/admin/region-dict/all', {
    method: 'GET',
  });
}

/**
 * 批量更新区域字典状态
 */
export async function batchUpdateRegionDictStatus(
  params: API.BatchStatusUpdateParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/region-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用区域字典
 */
export async function toggleRegionDictStatus(
  id: number,
): Promise<API.ResType<API.StatusToggleResponse>> {
  return request(`/admin/region-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}

/**
 * 类型字典相关接口
 */

/**
 * 创建类型字典
 */
export async function createTypeDict(
  params: API.CreateTypeDictParams,
): Promise<API.ResType<API.TypeDict>> {
  return request('/admin/type-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新类型字典
 */
export async function updateTypeDict(
  id: number,
  params: API.UpdateTypeDictParams,
): Promise<API.ResType<API.TypeDict>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除类型字典
 */
export async function deleteTypeDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取类型字典详情
 */
export async function getTypeDictDetail(
  id: number,
): Promise<API.ResType<API.TypeDict>> {
  return request(`/admin/type-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取类型字典列表（分页）
 */
export async function getTypeDictList(
  params?: API.GetTypeDictListParams,
): Promise<API.ResType<API.TypeDictListResponse>> {
  return request('/admin/type-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取类型字典树形结构
 */
export async function getTypeDictTree(): Promise<API.ResType<API.TypeDict[]>> {
  return request('/openapi/type-dict/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有类型字典（缓存）
 */
export async function getAllTypeDict(): Promise<API.ResType<API.TypeDict[]>> {
  return request('/admin/type-dict/all', {
    method: 'GET',
  });
}

/**
 * 批量更新类型字典状态
 */
export async function batchUpdateTypeDictStatus(
  params: API.BatchStatusUpdateParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/type-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用类型字典
 */
export async function toggleTypeDictStatus(
  id: number,
): Promise<API.ResType<API.StatusToggleResponse>> {
  return request(`/admin/type-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}

/**
 * 关系字典相关接口
 */

/**
 * 创建关系字典
 */
export async function createRelationshipDict(
  params: API.CreateRelationshipDictParams,
): Promise<API.ResType<API.RelationshipDict>> {
  return request('/admin/relationship-dict/', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新关系字典
 */
export async function updateRelationshipDict(
  id: number,
  params: API.UpdateRelationshipDictParams,
): Promise<API.ResType<API.RelationshipDict>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除关系字典
 */
export async function deleteRelationshipDict(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取关系字典详情
 */
export async function getRelationshipDictDetail(
  id: number,
): Promise<API.ResType<API.RelationshipDict>> {
  return request(`/admin/relationship-dict/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取关系字典列表（分页）
 */
export async function getRelationshipDictList(
  params?: API.GetRelationshipDictListParams,
): Promise<API.ResType<API.RelationshipDictListResponse>> {
  return request('/admin/relationship-dict/', {
    method: 'GET',
    params,
  });
}

/**
 * 获取区域字典树形结构
 */
export async function getRelationshipDictTree(): Promise<
  API.ResType<API.RelationshipDict[]>
> {
  return request('/openapi/relationship/tree', {
    method: 'GET',
  });
}

/**
 * 获取所有关系字典（缓存）
 */
export async function getAllRelationshipDict(): Promise<
  API.ResType<API.RelationshipDict[]>
> {
  return request('/admin/relationship-dict/all', {
    method: 'GET',
  });
}

// ==================== 公开接口（门户） ====================

export async function getPublicRegionDictTree(): Promise<
  API.ResType<API.RegionDict[]>
> {
  return request('/openapi/region-dict/tree', { method: 'GET' });
}

export async function getPublicTypeDictTree(): Promise<
  API.ResType<API.TypeDict[]>
> {
  return request('/openapi/type-dict/tree', { method: 'GET' });
}

export async function getPublicAllRegionDict(): Promise<
  API.ResType<API.RegionDict[]>
> {
  return request('/openapi/region-dict/all', { method: 'GET' });
}

export async function getPublicAllTypeDict(): Promise<
  API.ResType<API.TypeDict[]>
> {
  return request('/openapi/type-dict/all', { method: 'GET' });
}

/**
 * 批量更新关系字典状态
 */
export async function batchUpdateRelationshipDictStatus(
  params: API.BatchStatusUpdateParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/relationship-dict/batch-status', {
    method: 'PUT',
    data: params,
  });
}

/**
 * 启用/禁用关系字典
 */
export async function toggleRelationshipDictStatus(
  id: number,
): Promise<API.ResType<API.StatusToggleResponse>> {
  return request(`/admin/relationship-dict/${id}/toggle-status`, {
    method: 'POST',
  });
}
