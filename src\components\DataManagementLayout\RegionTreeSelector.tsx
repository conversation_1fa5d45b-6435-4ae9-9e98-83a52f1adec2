/**
 * @file 区域树形选择器组件
 * @description 用于显示和选择区域的树形组件
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */
import { DictionaryState } from '@/models/dictionary';
import {
  ApartmentOutlined,
  BankOutlined,
  CaretRightOutlined,
  GlobalOutlined,
  HomeOutlined,
} from '@ant-design/icons';
import { connect, styled, useDispatch } from '@umijs/max';
import { Empty, Spin, Tree } from 'antd';
import type { DataNode } from 'antd/es/tree';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

// 轻量化样式组件定义
const StyledTreeContainer = styled.div`
  .ant-tree {
    background: transparent;
    font-size: 13px;

    .ant-tree-treenode {
      padding: 0;
      margin: 2px 0;

      &:hover {
        background: transparent;
      }

      .ant-tree-node-content-wrapper {
        padding: 8px 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(0, 0, 0, 0.06);
        transition: all 0.2s ease;
        min-height: 36px;
        display: flex;
        align-items: center;

        &:hover {
          background: rgba(24, 144, 255, 0.08);
          border-color: rgba(24, 144, 255, 0.2);
          transform: translateX(2px);
        }

        &.ant-tree-node-selected {
          background: #1890ff;
          color: white;
          border-color: #1890ff;

          .node-icon {
            color: white !important;
          }

          .node-name {
            color: white !important;
          }

          .children-badge {
            background: rgba(255, 255, 255, 0.2) !important;
            border-color: rgba(255, 255, 255, 0.3) !important;
            color: white !important;
          }
        }
      }

      .ant-tree-switcher {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 6px;

        .ant-tree-switcher-icon {
          width: 16px;
          height: 16px;
          border-radius: 4px;
          background: rgba(24, 144, 255, 0.1);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          svg {
            font-size: 10px;
            color: #1890ff;
            transition: all 0.2s ease;
          }

          &:hover {
            background: #1890ff;

            svg {
              color: white;
            }
          }
        }

        &.ant-tree-switcher_open .ant-tree-switcher-icon {
          background: #1890ff;
          transform: rotate(90deg);

          svg {
            color: white;
          }
        }
      }

      .ant-tree-switcher-noop {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 6px;

        &::after {
          content: '';
          width: 4px;
          height: 4px;
          border-radius: 50%;
          background: #1890ff;
          opacity: 0.5;
        }
      }
    }

    .ant-tree-child-tree {
      margin-left: 16px;
      padding-left: 0;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -8px;
        top: -18px;
        bottom: 50%;
        width: 1px;
        background: rgba(24, 144, 255, 0.2);
        border-radius: 1px;
      }
    }
  }
`;

const AllRegionsNode = styled.div<{ selected?: boolean }>`
  padding: 8px 12px;
  margin: 0 0 8px 0;
  border-radius: 8px;
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 36px;
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 13px;

  .leaf-dot {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #1890ff;
    margin-right: 8px;
    flex-shrink: 0;
    opacity: 0.6;
  }

  .global-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: rgba(24, 144, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
    transition: all 0.2s ease;

    .anticon {
      font-size: 12px;
      color: #1890ff;
      transition: all 0.2s ease;
    }
  }

  .node-name {
    flex: 1;
    color: #262626;
    transition: all 0.2s ease;
  }

  &:hover {
    background: rgba(24, 144, 255, 0.08);
    border-color: rgba(24, 144, 255, 0.2);
    transform: translateX(2px);

    .global-icon {
      background: #1890ff;

      .anticon {
        color: white;
      }
    }

    .node-name {
      color: #1890ff;
    }
  }

  ${(props) =>
    props.selected &&
    `
    background: #1890ff;
    color: white;
    border-color: #1890ff;

    .global-icon {
      background: rgba(255, 255, 255, 0.2);

      .anticon {
        color: white;
      }
    }

    .node-name {
      color: white;
    }
  `}
`;

const NodeContent = styled.div<{
  level: number;
  nodeType: 'province' | 'city' | 'district';
}>`
  display: flex;
  align-items: center;
  width: 100%;

  .node-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: ${(props) => {
      const colors = {
        province: 'rgba(114, 46, 209, 0.1)',
        city: 'rgba(24, 144, 255, 0.1)',
        district: 'rgba(82, 196, 26, 0.1)',
      };
      return colors[props.nodeType];
    }};
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    flex-shrink: 0;
    transition: all 0.2s ease;

    .anticon {
      font-size: 12px;
      color: ${(props) => {
        const colors = {
          province: '#722ed1',
          city: '#1890ff',
          district: '#52c41a',
        };
        return colors[props.nodeType];
      }};
      transition: all 0.2s ease;
    }
  }

  .node-name {
    flex: 1;
    font-weight: ${(props) => {
      const weights = {
        province: 500,
        city: 450,
        district: 400,
      };
      return weights[props.nodeType];
    }};
    font-size: 13px;
    color: #262626;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .children-badge {
    margin-left: 8px;
    padding: 1px 6px;
    background: ${(props) => {
      const colors = {
        province: 'rgba(114, 46, 209, 0.1)',
        city: 'rgba(24, 144, 255, 0.1)',
        district: 'rgba(82, 196, 26, 0.1)',
      };
      return colors[props.nodeType];
    }};
    border-radius: 8px;
    border: 1px solid
      ${(props) => {
        const colors = {
          province: '#722ed1',
          city: '#1890ff',
          district: '#52c41a',
        };
        return colors[props.nodeType];
      }};
    transition: all 0.2s ease;
    flex-shrink: 0;

    font-size: 10px;
    font-weight: 500;
    color: ${(props) => {
      const colors = {
        province: '#722ed1',
        city: '#1890ff',
        district: '#52c41a',
      };
      return colors[props.nodeType];
    }};
    line-height: 1.2;
    min-width: 16px;
    text-align: center;
  }
`;

export interface RegionTreeNode {
  id: number;
  name: string;
  code?: string;
  parentId?: number;
  children?: RegionTreeNode[];
  level?: number;
}

export interface RegionTreeSelectorProps {
  /** 当前选中的区域ID */
  selectedRegionId?: number;
  /** 区域选择变化回调 */
  onRegionChange?: (regionId?: number) => void;
  /** 字典状态 */
  dictionary?: DictionaryState;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const RegionTreeSelector: React.FC<RegionTreeSelectorProps> = ({
  selectedRegionId,
  onRegionChange,
  dictionary,
  className,
  style,
}) => {
  const dispatch = useDispatch();
  const [expandedKeys, setExpandedKeys] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);

  // 加载区域字典数据
  useEffect(() => {
    if (!dictionary?.regionList?.length) {
      setLoading(true);
      dispatch({
        type: 'dictionary/fetchRegionList',
        payload: {},
      });
      // 由于dva的effects是异步的，我们需要监听loading状态
      setTimeout(() => {
        setLoading(false);
      }, 100);
    }
  }, [dispatch, dictionary?.regionList]);

  // 构建树形数据
  const treeData = useMemo(() => {
    if (!dictionary?.regionList?.length) return [];

    const regionMap = new Map<number, RegionTreeNode>();
    const rootNodes: RegionTreeNode[] = [];

    // 先创建所有节点
    dictionary.regionList.forEach((item) => {
      regionMap.set(item.id, {
        id: item.id,
        name: item.regionName,
        code: item.regionCode,
        parentId: item.parentId || undefined,
        children: [],
      });
    });

    // 构建树形结构
    regionMap.forEach((node) => {
      if (node.parentId) {
        const parent = regionMap.get(node.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(node);
        }
      } else {
        rootNodes.push(node);
      }
    });

    // 排序
    const sortNodes = (nodes: RegionTreeNode[]) => {
      nodes.sort((a, b) => a.name.localeCompare(b.name));
      nodes.forEach((node) => {
        if (node.children?.length) {
          sortNodes(node.children);
        }
      });
    };

    sortNodes(rootNodes);
    return rootNodes;
  }, [dictionary?.regionList]);

  // 转换为 Ant Design Tree 数据格式
  const convertToAntTreeData = useCallback(
    (nodes: RegionTreeNode[], level: number = 0): DataNode[] => {
      return nodes.map((node) => {
        const getNodeType = (
          level: number,
        ): 'province' | 'city' | 'district' => {
          if (level === 0) return 'province';
          if (level === 1) return 'city';
          return 'district';
        };

        const getNodeIcon = (nodeType: 'province' | 'city' | 'district') => {
          switch (nodeType) {
            case 'province':
              return <BankOutlined />;
            case 'city':
              return <ApartmentOutlined />;
            case 'district':
              return <HomeOutlined />;
          }
        };

        const nodeType = getNodeType(level);
        const hasChildren = node.children && node.children.length > 0;
        const childrenCount = hasChildren ? node.children!.length : 0;

        return {
          key: node.id.toString(),
          title: (
            <NodeContent level={level} nodeType={nodeType}>
              <div className="node-icon">{getNodeIcon(nodeType)}</div>
              <div className="node-name">{node.name}</div>
              {hasChildren && (
                <div className="children-badge">{childrenCount}</div>
              )}
            </NodeContent>
          ),
          children: hasChildren
            ? convertToAntTreeData(node.children!, level + 1)
            : undefined,
        };
      });
    },
    [],
  );

  const antTreeData = useMemo(
    () => convertToAntTreeData(treeData),
    [treeData, convertToAntTreeData],
  );

  // 处理节点选择
  const handleSelect = useCallback(
    (regionId?: number) => {
      onRegionChange?.(regionId);
    },
    [onRegionChange],
  );

  // 默认展开第一级节点
  useEffect(() => {
    if (treeData.length > 0 && expandedKeys.size === 0) {
      const firstLevelIds = treeData.map((node) => node.id);
      setExpandedKeys(new Set(firstLevelIds));
    }
  }, [treeData, expandedKeys.size]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
      </div>
    );
  }

  if (!treeData.length) {
    return <Empty description="暂无区域数据" />;
  }

  return (
    <StyledTreeContainer
      className={`region-tree-selector ${className || ''}`}
      style={style}
    >
      {/* 全部区域选项 */}
      <AllRegionsNode
        selected={!selectedRegionId}
        onClick={() => handleSelect(undefined)}
      >
        <div className="leaf-dot" />
        <div className="global-icon">
          <GlobalOutlined />
        </div>
        <div className="node-name">全部区域</div>
      </AllRegionsNode>

      {/* Ant Design Tree */}
      <Tree
        treeData={antTreeData}
        selectedKeys={selectedRegionId ? [selectedRegionId.toString()] : []}
        expandedKeys={Array.from(expandedKeys).map((id) => id.toString())}
        onSelect={(selectedKeys) => {
          if (selectedKeys.length > 0) {
            const selectedId = parseInt(selectedKeys[0] as string);
            handleSelect(selectedId);
          }
        }}
        onExpand={(expandedKeys) => {
          const expandedIds = expandedKeys.map((key) =>
            parseInt(key as string),
          );
          setExpandedKeys(new Set(expandedIds));
        }}
        showLine={false}
        showIcon={false}
        switcherIcon={<CaretRightOutlined />}
        blockNode
      />
    </StyledTreeContainer>
  );
};

export default connect(({ dictionary }: { dictionary: DictionaryState }) => ({
  dictionary,
}))(RegionTreeSelector);
