import { message } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useWaterSystemData } from './useWaterSystemData';
import { useWaterSystemFilters } from './useWaterSystemFilters';
import { useWaterSystemOperations } from './useWaterSystemOperations';

export interface UseWaterSystemManagerReturn {
  // 数据状态
  data: API.WaterSystem[];
  statistics: any;
  // 加载状态
  loading: boolean;
  statisticsLoading: boolean;
  operationLoading: boolean;
  batchLoading: boolean;

  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };

  // 筛选状态
  searchKeyword: string;
  regionFilter: number | undefined;
  typeFilter: number | undefined;
  selectedRowKeys: React.Key[];

  // 表单状态
  modalVisible: boolean;
  editingItem: API.WaterSystem | null;
  importModalVisible: boolean;
  detailModalVisible: boolean;
  detailRecord: API.WaterSystem | null;

  // 标签页状态
  activeTab: string;

  // 操作函数
  handleAdd: () => void;
  handleView: (record: API.WaterSystem) => void;
  handleEdit: (record: API.WaterSystem) => void;
  handleDelete: (id: number) => void;
  handleSubmit: (values: any) => void;
  handleModalCancel: () => void;
  handleDetailModalClose: () => void;

  // 搜索筛选函数
  handleSearch: (value: string) => void;
  handleRegionFilterChange: (value: number | undefined) => void;
  handleTypeFilterChange: (value: number | undefined) => void;
  handleRefresh: () => void;
  handleReset: () => void;

  // 分页函数
  handlePageChange: (page: number, pageSize: number) => void;

  // 选择函数
  handleSelectionChange: (selectedRowKeys: React.Key[]) => void;
  handleBatchDelete: () => void;
  handleClearSelection: () => void;

  // 标签页函数
  handleTabChange: (activeKey: string) => void;

  // 导入导出函数
  handleImportModalOpen: () => void;
  handleImportModalClose: () => void;
  handleDownloadTemplate: () => void;
  handlePreviewExcel: (file: File) => Promise<any>;
  handleImportExcel: (file: File) => Promise<any>;
  handleBatchImport: (importData: string) => void;
}

export const useWaterSystemManager = (): UseWaterSystemManagerReturn => {
  // 使用各个子Hook
  const dataHook = useWaterSystemData();
  const filtersHook = useWaterSystemFilters();
  const operationsHook = useWaterSystemOperations();

  // 表单状态
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<API.WaterSystem | null>(null);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailRecord, setDetailRecord] = useState<API.WaterSystem | null>(
    null,
  );

  // 标签页状态
  const [activeTab, setActiveTab] = useState('list');

  // 选择状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 初始化数据
  useEffect(() => {
    dataHook.fetchData(1, 10);
  }, []);

  // 当标签页切换时，获取对应数据
  useEffect(() => {
    const { regionFilter } = filtersHook;

    if (activeTab === 'statistics') {
      dataHook.fetchStatistics(regionFilter);
    }
  }, [activeTab, filtersHook.regionFilter]);

  // 表单操作
  const handleAdd = useCallback(() => {
    setEditingItem(null);
    setModalVisible(true);
  }, []);

  const handleView = useCallback((record: API.WaterSystem) => {
    setDetailRecord(record);
    setDetailModalVisible(true);
  }, []);

  const handleDetailModalClose = useCallback(() => {
    setDetailModalVisible(false);
    setDetailRecord(null);
  }, []);

  const handleEdit = useCallback((record: API.WaterSystem) => {
    setEditingItem(record);
    setModalVisible(true);
  }, []);

  const handleDelete = useCallback(
    async (id: number) => {
      const result = await operationsHook.deleteElement(id);
      if (result.success) {
        const filterParams = filtersHook.getFilterParams();
        dataHook.fetchData(
          dataHook.pagination.current,
          dataHook.pagination.pageSize,
          filterParams.keyword,
          filterParams.regionId,
          filterParams.typeId,
        );
      }
    },
    [operationsHook, filtersHook, dataHook],
  );

  const handleSubmit = useCallback(
    async (values: any) => {
      let result;
      if (editingItem) {
        result = await operationsHook.updateElement(editingItem.id, values);
      } else {
        result = await operationsHook.createElement(
          values as API.CreateWaterSystemParams,
        );
      }

      if (result.success) {
        setModalVisible(false);
        const filterParams = filtersHook.getFilterParams();
        dataHook.fetchData(
          dataHook.pagination.current,
          dataHook.pagination.pageSize,
          filterParams.keyword,
          filterParams.regionId,
          filterParams.typeId,
        );
      }
    },
    [editingItem, operationsHook, filtersHook, dataHook],
  );

  const handleModalCancel = useCallback(() => {
    setModalVisible(false);
    setEditingItem(null);
  }, []);

  // 搜索筛选操作
  const handleSearch = useCallback(
    (value: string) => {
      filtersHook.setSearchKeyword(value);
      const filterParams = { ...filtersHook.getFilterParams(), keyword: value };
      dataHook.fetchData(
        1,
        dataHook.pagination.pageSize,
        filterParams.keyword,
        filterParams.regionId,
        filterParams.typeId,
      );
    },
    [filtersHook, dataHook],
  );

  const handleRegionFilterChange = useCallback(
    (value: number | undefined) => {
      console.log('🔍 handleRegionFilterChange called with value:', value);

      filtersHook.setRegionFilter(value);

      // 直接构建参数，不依赖可能过时的状态
      const params: {
        keyword?: string;
        regionId?: number;
        typeId?: number;
      } = {};

      // 获取当前的搜索关键词和类型筛选（这些不会因为区域变化而改变）
      if (filtersHook.searchKeyword.trim()) {
        params.keyword = filtersHook.searchKeyword.trim();
      }

      // 直接使用传入的 value 作为 regionId
      if (value) {
        params.regionId = value;
      }

      if (filtersHook.typeFilter) {
        params.typeId = filtersHook.typeFilter;
      }

      console.log('📤 Calling fetchData with params:', params);

      dataHook.fetchData(
        1,
        dataHook.pagination.pageSize,
        params.keyword,
        params.regionId, // 当 value 为 undefined 时，这里也是 undefined
        params.typeId,
      );
    },
    [filtersHook, dataHook],
  );

  const handleTypeFilterChange = useCallback(
    (value: number | undefined) => {
      filtersHook.setTypeFilter(value);
      const filterParams = { ...filtersHook.getFilterParams(), typeId: value };
      dataHook.fetchData(
        1,
        dataHook.pagination.pageSize,
        filterParams.keyword,
        filterParams.regionId,
        filterParams.typeId,
      );
    },
    [filtersHook, dataHook],
  );

  const handleRefresh = useCallback(() => {
    const filterParams = filtersHook.getFilterParams();
    dataHook.fetchData(
      dataHook.pagination.current,
      dataHook.pagination.pageSize,
      filterParams.keyword,
      filterParams.regionId,
      filterParams.typeId,
    );
  }, [filtersHook, dataHook]);

  const handleReset = useCallback(() => {
    filtersHook.resetFilters();
    dataHook.fetchData(1, dataHook.pagination.pageSize);
  }, [filtersHook, dataHook]);

  // 分页操作
  const handlePageChange = useCallback(
    (page: number, pageSize: number) => {
      const filterParams = filtersHook.getFilterParams();
      dataHook.fetchData(
        page,
        pageSize,
        filterParams.keyword,
        filterParams.regionId,
        filterParams.typeId,
      );
    },
    [filtersHook, dataHook],
  );

  // 选择操作
  const handleSelectionChange = useCallback((keys: React.Key[]) => {
    setSelectedRowKeys(keys);
  }, []);

  const handleBatchDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要删除的项目');
      return;
    }

    const ids = selectedRowKeys.map((key) => Number(key));
    const result = await operationsHook.batchDeleteElements(ids);

    if (result.success) {
      setSelectedRowKeys([]);
      const filterParams = filtersHook.getFilterParams();
      dataHook.fetchData(
        dataHook.pagination.current,
        dataHook.pagination.pageSize,
        filterParams.keyword,
        filterParams.regionId,
        filterParams.typeId,
      );
    }
  }, [selectedRowKeys, operationsHook, filtersHook, dataHook]);

  const handleClearSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 标签页操作
  const handleTabChange = useCallback((activeKey: string) => {
    setActiveTab(activeKey);
  }, []);

  // 导入导出操作
  const handleImportModalOpen = useCallback(() => {
    setImportModalVisible(true);
  }, []);

  const handleImportModalClose = useCallback(() => {
    setImportModalVisible(false);
  }, []);

  const handleDownloadTemplate = useCallback(async () => {
    await operationsHook.downloadTemplate();
  }, [operationsHook]);

  const handlePreviewExcel = useCallback(
    async (file: File) => {
      return await operationsHook.previewExcel(file);
    },
    [operationsHook],
  );

  const handleImportExcel = useCallback(
    async (file: File) => {
      const result = await operationsHook.importExcel(file);
      if (result.success) {
        handleImportModalClose();
        dataHook.fetchData(1, dataHook.pagination.pageSize);
      }
      return result;
    },
    [operationsHook, dataHook],
  );

  const handleBatchImport = useCallback(
    async (importData: string) => {
      if (!importData || importData.trim() === '') {
        message.error('请输入导入数据');
        return;
      }

      // 解析JSON数据
      let elements;
      try {
        elements = JSON.parse(importData);
        if (!Array.isArray(elements)) {
          throw new Error('数据格式错误，应为数组格式');
        }
      } catch (parseError) {
        message.error('JSON格式错误，请检查数据格式');
        return;
      }

      const result = await operationsHook.batchImportElements(elements);

      if (result.success) {
        handleImportModalClose();
        dataHook.fetchData(1, dataHook.pagination.pageSize);
      }
    },
    [operationsHook, dataHook],
  );

  return {
    // 数据状态
    data: dataHook.data,
    statistics: dataHook.statistics,

    // 加载状态
    loading: dataHook.loading,
    statisticsLoading: dataHook.statisticsLoading,
    operationLoading: operationsHook.operationLoading,
    batchLoading: operationsHook.batchLoading,

    // 分页状态
    pagination: dataHook.pagination,

    // 筛选状态
    searchKeyword: filtersHook.searchKeyword,
    regionFilter: filtersHook.regionFilter,
    typeFilter: filtersHook.typeFilter,
    selectedRowKeys,

    // 表单状态
    modalVisible,
    editingItem,
    importModalVisible,
    detailModalVisible,
    detailRecord,

    // 标签页状态
    activeTab,

    // 操作函数
    handleAdd,
    handleView,
    handleEdit,
    handleDelete,
    handleSubmit,
    handleModalCancel,
    handleDetailModalClose,

    // 搜索筛选函数
    handleSearch,
    handleRegionFilterChange,
    handleTypeFilterChange,
    handleRefresh,
    handleReset,

    // 分页函数
    handlePageChange,

    // 选择函数
    handleSelectionChange,
    handleBatchDelete,
    handleClearSelection,

    // 标签页函数
    handleTabChange,

    // 导入导出函数
    handleImportModalOpen,
    handleImportModalClose,
    handleDownloadTemplate,
    handlePreviewExcel,
    handleImportExcel,
    handleBatchImport,
  };
};
