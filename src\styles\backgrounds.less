/* 背景图样式管理 */

/* 首页Hero背景图 */
.hero-background {
  background-image: linear-gradient(
      135deg,
      rgba(102, 126, 234, 70%) 0%,
      rgba(118, 75, 162, 70%) 50%,
      rgba(240, 147, 251, 70%) 100%
    ),
    url('/backgrounds/hero/guanzhong-landscape.jpg');
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-repeat: no-repeat;

  @media (max-width: 768px) {
    background-attachment: scroll;
    background-image: linear-gradient(
        135deg,
        rgba(102, 126, 234, 70%) 0%,
        rgba(118, 75, 162, 70%) 50%,
        rgba(240, 147, 251, 70%) 100%
      ),
      url('/backgrounds/hero/guanzhong-landscape-mobile.jpg');
  }
}

/* 山塬页面背景图 */
.mountain-background {
  background-image: linear-gradient(
      135deg,
      rgba(139, 69, 19, 60%) 0%,
      rgba(160, 82, 45, 60%) 100%
    ),
    url('/backgrounds/mountain/loess-plateau.jpg');
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-repeat: no-repeat;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
}

/* 水系页面背景图 */
.water-background {
  background-image: linear-gradient(
      135deg,
      rgba(30, 144, 255, 60%) 0%,
      rgba(0, 191, 255, 60%) 100%
    ),
    url('/backgrounds/water/weihe-river.jpg');
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-repeat: no-repeat;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
}

/* 历史要素页面背景图 */
.history-background {
  background-image: linear-gradient(
      135deg,
      rgba(184, 134, 11, 60%) 0%,
      rgba(146, 64, 14, 60%) 100%
    ),
    url('/backgrounds/history/terracotta-warriors.jpg');
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-repeat: no-repeat;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
}

/* 数字化页面背景图 */
.digital-background {
  background-image: linear-gradient(
      135deg,
      rgba(16, 185, 129, 60%) 0%,
      rgba(5, 150, 105, 60%) 100%
    ),
    url('/backgrounds/digital/modern-xian.jpg');
  background-size: cover;
  background-position: center center;
  background-attachment: fixed;
  background-repeat: no-repeat;

  @media (max-width: 768px) {
    background-attachment: scroll;
  }
}

/* 通用页面头部背景 */
.page-header-background {
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 30%);
    z-index: 1;
  }

  .page-header-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 24px;

    .page-title {
      font-size: 3rem;
      font-weight: 800;
      margin-bottom: 16px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 50%);
      background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .page-description {
      font-size: 1.2rem;
      color: rgba(255, 255, 255, 90%);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }
}

/* WebP支持检测 */
.webp .hero-background {
  background-image: linear-gradient(
      135deg,
      rgba(102, 126, 234, 70%) 0%,
      rgba(118, 75, 162, 70%) 50%,
      rgba(240, 147, 251, 70%) 100%
    ),
    url('/backgrounds/hero/guanzhong-landscape.webp');
}

.webp .mountain-background {
  background-image: linear-gradient(
      135deg,
      rgba(139, 69, 19, 60%) 0%,
      rgba(160, 82, 45, 60%) 100%
    ),
    url('/backgrounds/mountain/loess-plateau.webp');
}

.webp .water-background {
  background-image: linear-gradient(
      135deg,
      rgba(30, 144, 255, 60%) 0%,
      rgba(0, 191, 255, 60%) 100%
    ),
    url('/backgrounds/water/weihe-river.webp');
}

.webp .history-background {
  background-image: linear-gradient(
      135deg,
      rgba(184, 134, 11, 60%) 0%,
      rgba(146, 64, 14, 60%) 100%
    ),
    url('/backgrounds/history/terracotta-warriors.webp');
}

.webp .digital-background {
  background-image: linear-gradient(
      135deg,
      rgba(16, 185, 129, 60%) 0%,
      rgba(5, 150, 105, 60%) 100%
    ),
    url('/backgrounds/digital/modern-xian.webp');
}

/* 背景图预加载 */
.background-preloader {
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}
