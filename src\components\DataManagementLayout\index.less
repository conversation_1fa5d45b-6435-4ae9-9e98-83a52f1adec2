.data-management-layout {
  height: 100%;

  .region-selector-card {
    height: 100%;

    .ant-card-body {
      height: calc(100% - 57px);
      overflow-y: auto;
    }
  }

  .content-card {
    height: 100%;

    .ant-card-body {
      height: calc(100% - 57px);
      overflow-y: auto;
    }
  }
}

.region-tree-selector {
  padding: 12px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 50% 0%,
      rgba(24, 144, 255, 3%) 0%,
      transparent 70%
    );
    pointer-events: none;
  }

  .tree-node-wrapper {
    position: relative;
    margin: 2px 0;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      left: 32px;
      bottom: -2px;
      right: 20px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        #f0f0f0 20%,
        #f0f0f0 80%,
        transparent 100%
      );
      opacity: 0.5;
    }
  }

  .tree-connection-line {
    position: absolute;
    top: 0;
    bottom: 50%;
    width: 1px;
    background: linear-gradient(180deg, #e8e8e8 0%, transparent 100%);
    z-index: 1;
  }

  .region-tree-node {
    padding: 0;
    margin: 0;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    line-height: 1.5;
    display: flex;
    align-items: center;
    position: relative;
    border: 2px solid transparent;
    background: #fff;
    min-height: 56px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 4%);
    backdrop-filter: blur(10px);

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: transparent;
      border-radius: 12px 0 0 12px;
      transition: all 0.3s ease;
    }

    // 层级样式
    &.level-0 {
      font-weight: 600;
      font-size: 15px;
      min-height: 52px;
    }

    &.level-1 {
      font-weight: 500;
    }

    &.level-2 {
      font-weight: 400;
    }

    // 节点装饰器
    .node-decorator {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: var(--node-color, #1890ff);
      border-radius: 0 4px 4px 0;
      opacity: 0;
      transition: all 0.3s ease;
    }

    // 展开图标容器
    .expand-icon-container {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-left: 12px;

      .expand-icon-wrapper {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: var(--node-bg-color, rgba(24, 144, 255, 10%));
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        cursor: pointer;

        .expand-icon {
          font-size: 12px;
          color: var(--node-color, #1890ff);
          transition: all 0.3s ease;
        }

        &.expanded {
          background: var(--node-color, #1890ff);
          transform: rotate(90deg);

          .expand-icon {
            color: white;
          }
        }

        &:hover {
          background: var(--node-color, #1890ff);
          transform: scale(1.1);

          .expand-icon {
            color: white;
          }
        }
      }

      .leaf-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--node-color, #1890ff);
        opacity: 0.6;

        &.global-dot {
          background: linear-gradient(45deg, #1890ff, #40a9ff);
          box-shadow: 0 0 8px rgba(24, 144, 255, 40%);
        }
      }
    }

    // 节点图标容器
    .node-icon-container {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-left: 8px;

      .icon-background {
        width: 36px;
        height: 36px;
        border-radius: 12px;
        background: var(--node-bg-color, rgba(24, 144, 255, 10%));
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .anticon {
          font-size: 18px;
          color: var(--node-color, #1890ff);
          transition: all 0.3s ease;
        }

        &.global-icon-bg {
          background: linear-gradient(
            135deg,
            rgba(24, 144, 255, 10%),
            rgba(64, 169, 255, 10%)
          );
          border: 1px solid rgba(24, 144, 255, 20%);
        }
      }
    }

    // 节点内容
    .node-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-left: 12px;
      margin-right: 16px;
      min-width: 0;

      .node-name {
        flex: 1;
        font-weight: 500;
        color: #262626;
        transition: all 0.3s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 14px;
        line-height: 1.4;
      }

      .children-badge {
        margin-left: 12px;
        padding: 2px 8px;
        background: var(--node-bg-color, rgba(24, 144, 255, 10%));
        border-radius: 12px;
        border: 1px solid var(--node-color, #1890ff);
        transition: all 0.3s ease;

        .children-count {
          font-size: 11px;
          font-weight: 600;
          color: var(--node-color, #1890ff);
          line-height: 1;
        }
      }
    }

    // 右侧状态指示器
    .node-status-indicator {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: var(--node-color, #1890ff);
      opacity: 0;
      transition: all 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #f8fcff 0%, #e8f4ff 100%);
      border-color: rgba(24, 144, 255, 30%);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(24, 144, 255, 15%);

      .node-decorator {
        opacity: 1;
      }

      .node-status-indicator {
        opacity: 1;
      }

      .icon-background {
        background: var(--node-color, #1890ff) !important;
        transform: scale(1.1);

        .anticon {
          color: white !important;
        }
      }

      .node-name {
        color: var(--node-color, #1890ff);
        font-weight: 600;
      }

      .children-badge {
        background: var(--node-color, #1890ff);
        border-color: var(--node-color, #1890ff);

        .children-count {
          color: white;
        }
      }
    }

    &.selected {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      color: white;
      font-weight: 600;
      border-color: #1890ff;
      box-shadow: 0 6px 20px rgba(24, 144, 255, 30%);

      .node-decorator {
        opacity: 1;
        background: rgba(255, 255, 255, 90%);
      }

      .node-status-indicator {
        opacity: 1;
        background: rgba(255, 255, 255, 90%);
      }

      .expand-icon-wrapper {
        background: rgba(255, 255, 255, 20%) !important;

        .expand-icon {
          color: white !important;
        }
      }

      .leaf-dot {
        background: rgba(255, 255, 255, 80%) !important;
        box-shadow: 0 0 8px rgba(255, 255, 255, 40%);
      }

      .icon-background {
        background: rgba(255, 255, 255, 20%) !important;
        border-color: rgba(255, 255, 255, 30%);

        .anticon {
          color: white !important;
        }
      }

      .node-name {
        color: white;
        font-weight: 600;
      }

      .children-badge {
        background: rgba(255, 255, 255, 20%);
        border-color: rgba(255, 255, 255, 30%);

        .children-count {
          color: white;
        }
      }
    }

    // 节点类型特定样式
    &.province-node {
      --node-color: #722ed1;
      --node-bg-color: rgba(114, 46, 209, 10%);
    }

    &.city-node {
      --node-color: #1890ff;
      --node-bg-color: rgba(24, 144, 255, 10%);
    }

    &.district-node {
      --node-color: #52c41a;
      --node-bg-color: rgba(82, 196, 26, 10%);
    }

    &.all-regions {
      font-weight: 700;
      color: #262626;
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 16px;
      padding: 14px 20px;
      background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
      border-radius: 16px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 20px;
        right: 20px;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          #d9d9d9 20%,
          #d9d9d9 80%,
          transparent 100%
        );
      }

      // 全部区域特殊样式
      .global-icon {
        color: #1890ff;
      }

      .leaf-indicator {
        color: #1890ff;
        opacity: 0.6;
      }

      &:hover {
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
        color: #1890ff;

        &::before {
          background: linear-gradient(180deg, #40a9ff 0%, #1890ff 100%);
        }

        .global-icon {
          color: #40a9ff;
          transform: scale(1.1);
        }

        .level-indicator {
          opacity: 1;
          background: linear-gradient(
            180deg,
            #40a9ff 0%,
            #1890ff 50%,
            transparent 100%
          );
        }
      }

      &.selected {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        color: white;
        box-shadow: 0 8px 24px rgba(24, 144, 255, 30%);

        &::before {
          background: linear-gradient(
            180deg,
            #fff 0%,
            rgba(255, 255, 255, 80%) 100%
          );
        }

        &::after {
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 30%) 20%,
            rgba(255, 255, 255, 30%) 80%,
            transparent 100%
          );
        }

        .global-icon {
          color: white;
          transform: scale(1.15);
        }

        .level-indicator {
          opacity: 1;
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 80%) 0%,
            rgba(255, 255, 255, 40%) 50%,
            transparent 100%
          );
        }
      }
    }
  }

  .region-tree-children {
    margin-left: 0;
    padding-left: 0;
    position: relative;
    animation: slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    &::before {
      content: '';
      position: absolute;
      left: 32px;
      top: -28px;
      bottom: 50%;
      width: 2px;
      background: linear-gradient(
        180deg,
        var(--node-color, #1890ff) 0%,
        transparent 100%
      );
      opacity: 0.3;
      border-radius: 1px;
    }
  }

  // 动画效果
  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
}

// 山塬页面主题
.mountain-page .region-tree-selector {
  .region-tree-node {
    &:hover::before {
      background: linear-gradient(180deg, #87e8de 0%, #52c41a 100%);
    }

    &:hover {
      .region-node-icon {
        &.province-icon {
          color: #52c41a;
        }
        &.city-icon {
          color: #87e8de;
        }
        &.district-icon {
          color: #52c41a;
        }
        &.global-icon {
          color: #52c41a;
        }
      }

      .level-indicator {
        background: linear-gradient(
          180deg,
          #87e8de 0%,
          #52c41a 50%,
          transparent 100%
        );
      }
    }

    &.selected {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      color: white;
      border-color: #b7eb8f;

      &::before {
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 80%) 0%,
          rgba(255, 255, 255, 40%) 100%
        );
      }

      .region-node-icon {
        color: white;
      }
    }

    &.all-regions.selected {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      color: white;
    }
  }

  .expand-icon.expanded {
    color: #52c41a;
  }

  .region-tree-children::before {
    background: linear-gradient(
      180deg,
      #87e8de 0%,
      #52c41a 50%,
      transparent 100%
    );
  }
}

// 水系页面主题
.water-system-page .region-tree-selector {
  .region-tree-node {
    &:hover::before {
      background: linear-gradient(180deg, #1890ff 0%, #36cfc9 100%);
    }

    &:hover {
      .region-node-icon {
        &.province-icon {
          color: #1890ff;
        }
        &.city-icon {
          color: #36cfc9;
        }
        &.district-icon {
          color: #1890ff;
        }
        &.global-icon {
          color: #1890ff;
        }
      }

      .level-indicator {
        background: linear-gradient(
          180deg,
          #1890ff 0%,
          #36cfc9 50%,
          transparent 100%
        );
      }
    }

    &.selected {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      color: white;
      border-color: #91d5ff;

      &::before {
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 80%) 0%,
          rgba(255, 255, 255, 40%) 100%
        );
      }

      .region-node-icon {
        color: white;
      }
    }

    &.all-regions.selected {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      color: white;
    }
  }

  .expand-icon.expanded {
    color: #1890ff;
  }

  .region-tree-children::before {
    background: linear-gradient(
      180deg,
      #1890ff 0%,
      #36cfc9 50%,
      transparent 100%
    );
  }
}

// 历史要素页面主题
.historical-element-page .region-tree-selector {
  .region-tree-node {
    &:hover::before {
      background: linear-gradient(180deg, #faad14 0%, #d48806 100%);
    }

    &:hover {
      .region-node-icon {
        &.province-icon {
          color: #faad14;
        }
        &.city-icon {
          color: #d48806;
        }
        &.district-icon {
          color: #faad14;
        }
        &.global-icon {
          color: #faad14;
        }
      }

      .level-indicator {
        background: linear-gradient(
          180deg,
          #faad14 0%,
          #d48806 50%,
          transparent 100%
        );
      }
    }

    &.selected {
      background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
      color: white;
      border-color: #ffec3d;

      &::before {
        background: linear-gradient(
          180deg,
          rgba(255, 255, 255, 80%) 0%,
          rgba(255, 255, 255, 40%) 100%
        );
      }

      .region-node-icon {
        color: white;
      }
    }

    &.all-regions.selected {
      background: linear-gradient(135deg, #faad14 0%, #d48806 100%);
      color: white;
    }
  }

  .expand-icon.expanded {
    color: #faad14;
  }

  .region-tree-children::before {
    background: linear-gradient(
      180deg,
      #faad14 0%,
      #d48806 50%,
      transparent 100%
    );
  }
}

// 动画关键帧
@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }

  to {
    opacity: 0.4;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.4;
  }

  50% {
    opacity: 0.8;
  }
}

// 加载状态样式
.region-tree-loading {
  .region-tree-children::before {
    animation: pulse 2s infinite;
  }
}

// 空状态样式
.region-tree-empty {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;

  &::before {
    content: '📍';
    display: block;
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  &::after {
    content: '暂无区域数据';
    font-size: 14px;
    color: #bfbfbf;
  }
}
