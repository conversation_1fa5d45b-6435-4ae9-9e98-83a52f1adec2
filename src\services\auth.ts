import { request } from '@umijs/max';

// 认证相关的API接口

/**
 * 登录接口
 */
export async function login(
  params: API.LoginParams,
): Promise<API.ResType<API.LoginResponse>> {
  return request('/admin/auth/login', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(): Promise<API.ResType<API.CurrentUser>> {
  return request('/admin/auth/profile', {
    method: 'GET',
  });
}

/**
 * 退出登录
 */
export async function logout(): Promise<API.ResType<API.MessageResponse>> {
  return request('/admin/auth/logout', {
    method: 'POST',
  });
}

/**
 * 刷新Token
 */
export async function refreshToken(): Promise<
  API.ResType<API.MessageResponse>
> {
  return request('/admin/auth/refresh', {
    method: 'POST',
  });
}
