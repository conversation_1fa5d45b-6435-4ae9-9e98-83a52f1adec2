import { DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Space, Table, Tag, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React from 'react';

export interface WaterSystemTableProps {
  data: API.WaterSystem[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  selectedRowKeys: React.Key[];
  onView: (record: API.WaterSystem) => void;
  onEdit: (record: API.WaterSystem) => void;
  onDelete: (id: number) => void;
  onPageChange: (page: number, pageSize: number) => void;
  onSelectionChange: (selectedRowKeys: React.Key[]) => void;
}

export const WaterSystemTable: React.FC<WaterSystemTableProps> = ({
  data,
  loading,
  pagination,
  selectedRowKeys,
  onView,
  onEdit,
  onDelete,
  onPageChange,
  onSelectionChange,
}) => {
  const columns: ColumnsType<API.WaterSystem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
    },
    {
      title: '水系名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left',
      render: (text: string) => (
        <Tooltip title={text}>
          <span style={{ fontWeight: 500 }}>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => (
        <Tag color="blue" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      ),
    },
    // {
    //   title: '类型',
    //   dataIndex: 'typeDict',
    //   key: 'typeDict',
    //   width: 100,
    //   render: (typeDict: any) => (
    //     <Tag color="cyan">{typeDict?.typeName || '未分类'}</Tag>
    //   ),
    // },
    {
      title: '经度',
      dataIndex: 'longitude',
      key: 'longitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>
          {value?.toFixed(6) || '-'}
        </span>
      ),
    },
    {
      title: '纬度',
      dataIndex: 'latitude',
      key: 'latitude',
      width: 120,
      render: (value: number) => (
        <span style={{ fontFamily: 'monospace' }}>
          {value?.toFixed(6) || '-'}
        </span>
      ),
    },
    {
      title: '长度/面积',
      dataIndex: 'lengthArea',
      key: 'lengthArea',
      width: 120,
      render: (value: string) => (
        <span style={{ fontWeight: 500, color: '#1890ff' }}>
          {value || '-'}
        </span>
      ),
    },
    {
      title: '所属区域',
      dataIndex: 'regionDict',
      key: 'regionDict',
      width: 120,
      render: (regionDict: any) => (
        <Tag color="orange">{regionDict?.regionName || '未知区域'}</Tag>
      ),
    },
    {
      title: '历史记载',
      dataIndex: 'historicalRecords',
      key: 'historicalRecords',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text: string) => (
        <Tooltip title={text} placement="topLeft">
          <span>{text || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => (
        <span style={{ fontSize: '12px', color: '#666' }}>
          {text ? new Date(text).toLocaleString() : '-'}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onView(record);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                onEdit(record);
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个水系吗？"
              description="删除后将无法恢复，请谨慎操作。"
              onConfirm={() => onDelete(record.id)}
              okText="确定"
              cancelText="取消"
              placement="topRight"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectionChange,
    getCheckboxProps: (record: API.WaterSystem) => ({
      disabled: false,
      name: record.name,
    }),
  };

  return (
    <Table
      columns={columns}
      dataSource={data}
      rowKey="id"
      loading={loading}
      rowSelection={rowSelection}
      scroll={{ x: 1400, y: 'calc(100vh - 400px)' }}
      pagination={{
        current: pagination.current,
        pageSize: pagination.pageSize,
        total: pagination.total,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100'],
        onChange: onPageChange,
        onShowSizeChange: onPageChange,
      }}
      size="small"
      bordered
      style={{
        backgroundColor: '#fff',
        borderRadius: '8px',
        overflow: 'hidden',
      }}
    />
  );
};
