/* 要素关联表单样式 */

@primary-color: #1890ff;
@border-color-base: #d9d9d9;
@border-color-light: #f0f0f0;
@text-color: #262626;
@text-color-secondary: #595959;
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@border-radius-base: 6px;

.formContainer {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.sectionCard {
  margin-bottom: 16px;
  border: 1px solid @border-color-light;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 3%);
  transition: all 0.3s ease;
  animation: fade-in-up 0.3s ease-out;

  &:hover {
    border-color: @border-color-base;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 6%);
  }

  // 卡片标题样式
  :global(.ant-card-head-title) {
    font-weight: 600;
    color: @text-color;
  }

  // 卡片内容区域
  :global(.ant-card-body) {
    padding: 16px 20px;
  }
}

.sectionTitle {
  font-weight: 600;
  color: @text-color;
  margin-bottom: 0;

  // 图标样式
  .anticon {
    margin-right: 8px;
    color: @primary-color;
  }
}

.fieldLabel {
  font-weight: 500;
  color: @text-color-secondary;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tooltipIcon {
  color: @primary-color;
  cursor: help;
  font-size: 14px;

  &:hover {
    color: #40a9ff;
  }
}

.textArea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;

  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
  }
}

.modalTitle {
  font-size: 16px;
  font-weight: 600;
  color: @text-color;
  display: flex;
  align-items: center;
  gap: 8px;

  .anticon {
    color: @primary-color;
  }
}

// 表单项样式优化
.formItem {
  margin-bottom: 16px;

  // 必填项标记样式
  :global(.ant-form-item-required) {
    &::before {
      color: #ff4d4f;
      font-weight: bold;
    }
  }

  // 标签样式
  :global(.ant-form-item-label) {
    padding-bottom: 4px;

    > label {
      font-weight: 500;
      color: @text-color-secondary;
    }
  }

  // 输入框样式
  :global(.ant-input),
  :global(.ant-input-number),
  :global(.ant-picker) {
    border-radius: @border-radius-base;
    transition: all 0.3s ease;

    &:hover {
      border-color: @primary-color;
    }

    &:focus {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
    }
  }

  // 选择器样式
  :global(.ant-select) {
    .ant-select-selector {
      border-radius: @border-radius-base;
      transition: all 0.3s ease;

      &:hover {
        border-color: @primary-color;
      }
    }

    &.ant-select-focused {
      .ant-select-selector {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
      }
    }
  }
}

// 两列布局优化
.twoColumnRow {
  .ant-col {
    &:first-child {
      padding-right: 8px;
    }

    &:last-child {
      padding-left: 8px;
    }
  }
}

// 关联关系特殊样式
.relationFlow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16px 0;
  padding: 12px;
  background: linear-gradient(90deg, #f0f9ff 0%, #e0f2fe 50%, #f0f9ff 100%);
  border: 1px dashed @primary-color;
  border-radius: 8px;
  font-size: 14px;
  color: @text-color-secondary;

  .flowArrow {
    margin: 0 12px;
    color: @primary-color;
    font-size: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .formContainer {
    max-height: 60vh;
  }

  .sectionCard {
    margin-bottom: 12px;

    :global(.ant-card-body) {
      padding: 12px 16px;
    }
  }

  .twoColumnRow {
    .ant-col {
      &:first-child,
      &:last-child {
        padding-left: 0;
        padding-right: 0;
        margin-bottom: 8px;
      }
    }
  }
}

// 动画效果
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .sectionCard {
    background-color: #1f1f1f;
    border-color: #434343;

    &:hover {
      border-color: #595959;
    }
  }

  .sectionTitle,
  .fieldLabel {
    color: #fff;
  }

  .modalTitle {
    color: #fff;
  }

  .relationFlow {
    background: linear-gradient(90deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    border-color: @primary-color;
  }
}
