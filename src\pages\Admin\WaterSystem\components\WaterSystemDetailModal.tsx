import { Descriptions, Modal, Tag } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

export interface WaterSystemDetailModalProps {
  visible: boolean;
  record: API.WaterSystem | null;
  onClose: () => void;
}

export const WaterSystemDetailModal: React.FC<WaterSystemDetailModalProps> = ({
  visible,
  record,
  onClose,
}) => {
  if (!record) return null;

  return (
    <Modal
      title={`水系详情 - ${record.name}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Descriptions
        bordered
        column={2}
        size="middle"
        labelStyle={{ width: '120px', fontWeight: 'bold' }}
      >
        <Descriptions.Item label="ID" span={1}>
          {record.id}
        </Descriptions.Item>
        <Descriptions.Item label="水系名称" span={1}>
          {record.name}
        </Descriptions.Item>

        <Descriptions.Item label="编号" span={1}>
          {record.code || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="经度" span={1}>
          {record.longitude || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="纬度" span={1}>
          {record.latitude || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="特征" span={1}>
          {record.feature || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="长度/面积" span={1}>
          {record.lengthArea || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="所属区域" span={1}>
          {record.regionDict?.regionName || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="类型" span={2}>
          {record.typeDict?.typeName ? (
            <Tag color="cyan">{record.typeDict.typeName}</Tag>
          ) : (
            '-'
          )}
        </Descriptions.Item>

        <Descriptions.Item label="创建时间" span={1}>
          {record.createTime
            ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={1}>
          {record.updateTime
            ? dayjs(record.updateTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>

        {record.description && (
          <Descriptions.Item label="描述" span={2}>
            {record.description}
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};
