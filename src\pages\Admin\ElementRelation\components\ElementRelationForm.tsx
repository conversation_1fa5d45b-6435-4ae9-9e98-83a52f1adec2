import DictSelect from '@/components/DictSelect';
import { getAllHistoricalElements } from '@/services/historicalElement';
import { getAllMountains } from '@/services/mountain';
import { getAllWaterSystems } from '@/services/waterSystem';
import {
  ArrowRightOutlined,
  CompassOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  NodeIndexOutlined,
  ShareAltOutlined,
  TagOutlined,
} from '@ant-design/icons';
import {
  AutoComplete,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Space,
  Tooltip,
} from 'antd';

import React, { useEffect, useState } from 'react';
import styles from './ElementRelationForm.module.less';

const { TextArea } = Input;

export interface ElementRelationFormProps {
  initialValues?: Partial<API.ElementRelation>;
  onValuesChange?: (changedValues: any, allValues: any) => void;
  onFormRef?: (form: any) => void;
  readonly?: boolean;
}

const ElementRelationForm: React.FC<ElementRelationFormProps> = ({
  initialValues,
  onValuesChange,
  onFormRef,
  readonly = false,
}) => {
  const [form] = Form.useForm();
  const [sourceElements, setSourceElements] = useState<any[]>([]);
  const [targetElements, setTargetElements] = useState<any[]>([]);
  const [sourceType, setSourceType] = useState<string>('');
  const [targetType, setTargetType] = useState<string>('');
  const [targetEntityType, setTargetEntityType] = useState<string>('');

  // 要素类型选项
  const elementTypeOptions = [
    { label: '山塬', value: 'mountain' },
    { label: '水系', value: 'water_system' },
    { label: '历史要素', value: 'historical_element' },
  ];

  // 目标类型选项
  const targetTypeOptions = [
    { label: '具体要素', value: 'element' },
    { label: '类别', value: 'category' },
  ];

  // 目标要素类型选项
  const targetEntityTypeOptions = [
    { label: '山塬', value: 'mountain' },
    { label: '水系', value: 'water_system' },
    { label: '历史要素', value: 'historical_element' },
  ];

  // 常见方向选项
  const directionOptions = [
    { label: '前有', value: '前有' },
    { label: '后有', value: '后有' },
    { label: '上有', value: '上有' },
    { label: '下有', value: '下有' },
    { label: '左有', value: '左有' },
    { label: '右有', value: '右有' },
    { label: '东连', value: '东连' },
    { label: '西连', value: '西连' },
    { label: '南连', value: '南连' },
    { label: '北连', value: '北连' },
    { label: '相连', value: '相连' },
    { label: '绕其后', value: '绕其后' },
    { label: '绕其前', value: '绕其前' },
    { label: '面向', value: '面向' },
    { label: '背向', value: '背向' },
  ];

  // 加载源要素数据
  const loadSourceElements = async (type: string) => {
    try {
      let elements: any[] = [];
      switch (type) {
        case 'mountain':
          {
            const mountainRes = await getAllMountains();
            if (mountainRes.errCode === 0) {
              elements =
                mountainRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
        case 'water_system':
          {
            const waterRes = await getAllWaterSystems();
            if (waterRes.errCode === 0) {
              elements =
                waterRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
        case 'historical_element':
          {
            const historyRes = await getAllHistoricalElements();
            if (historyRes.errCode === 0) {
              elements =
                historyRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
      }
      setSourceElements(elements);
    } catch (error) {
      console.error('加载源要素数据失败:', error);
    }
  };

  // 加载目标要素数据
  const loadTargetElements = async (entityType: string) => {
    try {
      let elements: any[] = [];
      switch (entityType) {
        case 'mountain':
          {
            const mountainRes = await getAllMountains();
            if (mountainRes.errCode === 0) {
              elements =
                mountainRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
        case 'water_system':
          {
            const waterRes = await getAllWaterSystems();
            if (waterRes.errCode === 0) {
              elements =
                waterRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
        case 'historical_element':
          {
            const historyRes = await getAllHistoricalElements();
            if (historyRes.errCode === 0) {
              elements =
                historyRes.data?.map((item: any) => ({
                  label: item.name,
                  value: item.id,
                  code: item.code,
                })) || [];
            }
          }
          break;
      }
      setTargetElements(elements);
    } catch (error) {
      console.error('加载目标要素数据失败:', error);
    }
  };

  // 监听源要素类型变化
  const handleSourceTypeChange = (value: string) => {
    setSourceType(value);
    form.setFieldsValue({ sourceId: undefined });
    loadSourceElements(value);
  };

  // 监听目标类型变化
  const handleTargetTypeChange = (value: string) => {
    setTargetType(value);
    form.setFieldsValue({
      targetEntityType: undefined,
      targetId: undefined,
    });
    setTargetEntityType('');
  };

  // 监听目标要素类型变化
  const handleTargetEntityTypeChange = (value: string) => {
    setTargetEntityType(value);
    form.setFieldsValue({ targetId: undefined });
    if (
      value === 'mountain' ||
      value === 'water_system' ||
      value === 'historical_element'
    ) {
      loadTargetElements(value);
    }
  };

  // 初始化表单
  useEffect(() => {
    if (onFormRef) {
      onFormRef(form);
    }
  }, [form, onFormRef]);

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
      if (initialValues.sourceType) {
        setSourceType(initialValues.sourceType);
        loadSourceElements(initialValues.sourceType);
      }
      if (initialValues.targetType) {
        setTargetType(initialValues.targetType);
      }
      if (initialValues.targetEntityType) {
        setTargetEntityType(initialValues.targetEntityType);
        if (
          ['mountain', 'water_system', 'historical_element'].includes(
            initialValues.targetEntityType,
          )
        ) {
          loadTargetElements(initialValues.targetEntityType);
        }
      }
    }
  }, [initialValues, form]);

  // 渲染目标要素选择器
  const renderTargetElementSelect = () => {
    // 当目标类型为"类别"时，直接从type_dict字典中选择
    if (targetType === 'category') {
      return (
        <DictSelect.DictTreeSelect
          type="type"
          placeholder="请选择类型字典"
          allowClear
          disabled={readonly}
        />
      );
    }

    // 当目标类型为"具体要素"时，根据要素类型显示对应的选择器
    if (targetType === 'element') {
      if (targetEntityType === 'type_dict') {
        return (
          <DictSelect.DictTreeSelect
            type="type"
            placeholder="请选择类型字典"
            allowClear
            disabled={readonly}
          />
        );
      }

      if (targetEntityType === 'region_dict') {
        return (
          <DictSelect.DictTreeSelect
            type="region"
            placeholder="请选择区域字典"
            allowClear
            disabled={readonly}
          />
        );
      }

      return (
        <Select
          placeholder="请选择目标要素"
          allowClear
          showSearch
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={targetElements}
          disabled={readonly}
        />
      );
    }

    return null;
  };

  return (
    <div className={styles.formContainer}>
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={onValuesChange}
      >
        {/* 源要素信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <NodeIndexOutlined />
              源要素信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="sourceType"
                label={
                  <Space className={styles.fieldLabel}>
                    <TagOutlined />
                    要素类型
                  </Space>
                }
                rules={[{ required: true, message: '请选择源要素类型' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择源要素类型"
                  options={elementTypeOptions}
                  onChange={handleSourceTypeChange}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sourceId"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    具体要素
                  </Space>
                }
                rules={[{ required: true, message: '请选择源要素' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择源要素"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={sourceElements}
                  disabled={!sourceType || readonly}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 关联关系流向 */}
        <div className={styles.relationFlow}>
          <span>源要素</span>
          <ArrowRightOutlined className={styles.flowArrow} />
          <span>目标要素</span>
        </div>

        {/* 目标要素信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <ShareAltOutlined />
              目标要素信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="targetType"
                label={
                  <Space className={styles.fieldLabel}>
                    <TagOutlined />
                    目标类型
                  </Space>
                }
                rules={[{ required: true, message: '请选择目标类型' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择目标类型"
                  options={targetTypeOptions}
                  onChange={handleTargetTypeChange}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              {targetType === 'element' && (
                <Form.Item
                  name="targetEntityType"
                  label={
                    <Space className={styles.fieldLabel}>
                      <InfoCircleOutlined />
                      要素类型
                    </Space>
                  }
                  rules={[{ required: true, message: '请选择目标要素类型' }]}
                  className={styles.formItem}
                >
                  <Select
                    placeholder="请选择目标要素类型"
                    options={targetEntityTypeOptions}
                    onChange={handleTargetEntityTypeChange}
                    disabled={readonly}
                  />
                </Form.Item>
              )}
            </Col>
          </Row>

          <Form.Item
            name="targetId"
            label={
              <Space className={styles.fieldLabel}>
                <LinkOutlined />
                {targetType === 'category' ? '具体类型' : '具体要素'}
              </Space>
            }
            rules={[
              {
                required: true,
                message:
                  targetType === 'category'
                    ? '请选择类型字典'
                    : '请选择目标要素',
              },
            ]}
            className={styles.formItem}
          >
            {renderTargetElementSelect()}
          </Form.Item>
        </Card>

        {/* 关系信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <LinkOutlined />
              关系信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="relationDictId"
                label={
                  <Space className={styles.fieldLabel}>
                    <ShareAltOutlined />
                    关系类型
                    <Tooltip title="选择要素之间的关系类型">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <DictSelect.DictTreeSelect
                  type="relation"
                  placeholder="请选择关系类型"
                  allowClear
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              {/* TODO: 父级关系待定
              <Form.Item
                name="parentRelationshipId"
                label={
                  <Space className={styles.fieldLabel}>
                    <NodeIndexOutlined />
                    父级关系
                    <Tooltip title="选择父级关系，用于建立层级关系">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <InputNumber
                  placeholder="请输入父级关系ID"
                  min={1}
                  style={{ width: '100%' }}
                  disabled={readonly}
                />
              </Form.Item> */}
            </Col>
          </Row>

          <Form.Item
            name="direction"
            label={
              <Space className={styles.fieldLabel}>
                <CompassOutlined />
                关联方向
                <Tooltip title="描述要素之间的空间或逻辑方向关系，支持选择或手动输入">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            className={styles.formItem}
          >
            <AutoComplete
              placeholder="请选择或输入关联方向"
              allowClear
              options={directionOptions}
              disabled={readonly}
              filterOption={(inputValue, option) =>
                (option?.label ?? '')
                  .toLowerCase()
                  .includes(inputValue.toLowerCase())
              }
            />
          </Form.Item>
        </Card>

        {/* 详细信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <FileTextOutlined />
              详细信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Form.Item
            name="term"
            label={
              <Space className={styles.fieldLabel}>
                <TagOutlined />
                词条描述
                <Tooltip title="简短的词条或标签描述">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            className={styles.formItem}
          >
            <Input
              placeholder="请输入词条描述"
              maxLength={255}
              disabled={readonly}
            />
          </Form.Item>

          <Form.Item
            name="record"
            label={
              <Space className={styles.fieldLabel}>
                <FileTextOutlined />
                记载内容
                <Tooltip title="详细的文字记载或描述">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            className={styles.formItem}
          >
            <TextArea
              placeholder="请输入记载内容"
              rows={4}
              maxLength={1000}
              showCount
              disabled={readonly}
              className={styles.textArea}
            />
          </Form.Item>

          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="sort"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    排序号
                    <Tooltip title="用于控制显示顺序，数字越小越靠前">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                className={styles.formItem}
              >
                <InputNumber
                  placeholder="请输入排序号"
                  min={0}
                  style={{ width: '100%' }}
                  disabled={readonly}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label={
                  <Space className={styles.fieldLabel}>
                    <InfoCircleOutlined />
                    状态
                  </Space>
                }
                rules={[{ required: true, message: '请选择状态' }]}
                className={styles.formItem}
              >
                <Select
                  placeholder="请选择状态"
                  disabled={readonly}
                  options={[
                    { label: '启用', value: 1 },
                    { label: '禁用', value: 0 },
                  ]}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ElementRelationForm;
