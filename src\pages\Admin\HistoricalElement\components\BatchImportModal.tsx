import {
  DownloadOutlined,
  FileExcelOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import {
  Alert,
  Button,
  Card,
  Modal,
  Space,
  Steps,
  Table,
  Typography,
  Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Dragger } = Upload;
const { Paragraph, Text } = Typography;

export interface BatchImportModalProps {
  visible: boolean;
  loading: boolean;
  onDownloadTemplate: () => void;
  onPreview: (file: File) => Promise<{
    success: boolean;
    data?: API.ExcelImportPreviewResponse;
    message?: string;
  }>;
  onImport: (file: File) => Promise<{
    success: boolean;
    data?: API.ExcelImportResponse;
    message?: string;
  }>;
  onCancel: () => void;
}

export const BatchImportModal: React.FC<BatchImportModalProps> = ({
  visible,
  loading,
  onDownloadTemplate,
  onPreview,
  onImport,
  onCancel,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [previewData, setPreviewData] =
    useState<API.ExcelImportPreviewResponse | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  useEffect(() => {
    if (visible) {
      // 重置状态
      setCurrentStep(0);
      setUploadFile(null);
      setPreviewData(null);
      setFileList([]);
    }
  }, [visible]);

  const handleCancel = () => {
    setCurrentStep(0);
    setUploadFile(null);
    setPreviewData(null);
    setFileList([]);
    onCancel();
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls',
    beforeUpload: (file) => {
      const isExcel =
        file.type ===
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        Modal.error({
          title: '文件格式错误',
          content: '请上传Excel文件（.xlsx或.xls格式）',
        });
        return false;
      }

      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        Modal.error({
          title: '文件大小超限',
          content: '文件大小不能超过10MB',
        });
        return false;
      }

      setUploadFile(file);
      setFileList([
        {
          uid: file.uid,
          name: file.name,
          status: 'done',
          originFileObj: file,
        },
      ]);
      return false; // 阻止自动上传
    },
    onRemove: () => {
      setUploadFile(null);
      setFileList([]);
      setPreviewData(null);
      setCurrentStep(0);
    },
    fileList,
  };

  const handlePreview = async () => {
    if (!uploadFile) return;

    const result = await onPreview(uploadFile);
    if (result.success && result.data) {
      setPreviewData(result.data);
      setCurrentStep(1);
    }
  };

  const handleImport = async () => {
    if (!uploadFile) return;

    const result = await onImport(uploadFile);
    if (result.success) {
      handleCancel(); // 成功后关闭弹窗
    }
  };

  const errorColumns = [
    {
      title: '行号',
      dataIndex: 'row',
      key: 'row',
      width: 80,
    },
    {
      title: '字段',
      dataIndex: 'field',
      key: 'field',
      width: 120,
    },
    {
      title: '错误值',
      dataIndex: 'value',
      key: 'value',
      width: 150,
      render: (value: any) => String(value),
    },
    {
      title: '错误信息',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  const previewColumns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '编号',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '经度',
      dataIndex: 'constructionLongitude',
      key: 'constructionLongitude',
      width: 100,
    },
    {
      title: '纬度',
      dataIndex: 'constructionLatitude',
      key: 'constructionLatitude',
      width: 100,
    },
    {
      title: '位置描述',
      dataIndex: 'locationDescription',
      key: 'locationDescription',
      ellipsis: true,
    },
  ];

  const steps = [
    {
      title: '上传文件',
      description: '选择Excel文件并预览',
    },
    {
      title: '数据预览',
      description: '确认数据无误后导入',
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div>
            <Alert
              message="导入说明"
              description={
                <div>
                  <Paragraph>
                    1. 请下载模板文件，按照模板格式填写历史要素数据
                  </Paragraph>
                  <Paragraph>2. 支持 .xlsx 和 .xls 格式的Excel文件</Paragraph>
                  <Paragraph>3. 必填字段：名称、编号、所属区域</Paragraph>
                  <Paragraph>
                    4. 经度范围：-180 到 180，纬度范围：-90 到 90（可选）
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Card size="small" style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={onDownloadTemplate}
                  type="primary"
                  ghost
                >
                  下载模板
                </Button>
                <Text type="secondary">
                  <InfoCircleOutlined /> 请先下载模板文件，按格式填写数据
                </Text>
              </Space>
            </Card>

            <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
              <p className="ant-upload-drag-icon">
                <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持.xlsx和.xls格式，文件大小不超过10MB
              </p>
            </Dragger>
          </div>
        );

      case 1:
        return (
          <div>
            {previewData && (
              <div>
                <Card size="small" style={{ marginBottom: 16 }}>
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <span>总行数: {previewData.totalRows}</span>
                    <span>有效行数: {previewData.validRows}</span>
                    <span>
                      错误行数: {previewData.totalRows - previewData.validRows}
                    </span>
                  </div>
                </Card>

                {previewData.errors && previewData.errors.length > 0 && (
                  <div style={{ marginBottom: 16 }}>
                    <Alert
                      message="数据验证错误"
                      description="以下数据存在错误，请修正后重新上传"
                      type="error"
                      showIcon
                      style={{ marginBottom: 8 }}
                    />
                    <Table
                      columns={errorColumns}
                      dataSource={previewData.errors}
                      pagination={false}
                      size="small"
                      scroll={{ y: 200 }}
                      rowKey={(_, index) => `error-${index}`}
                    />
                  </div>
                )}

                <div>
                  <h4>数据预览（前5条）</h4>
                  <Table
                    columns={previewColumns}
                    dataSource={previewData.preview?.slice(0, 5) || []}
                    pagination={false}
                    size="small"
                    scroll={{ x: 600 }}
                    rowKey={(_, index) => `preview-${index}`}
                  />
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const getModalFooter = () => {
    switch (currentStep) {
      case 0:
        return [
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="preview"
            type="primary"
            onClick={handlePreview}
            disabled={!uploadFile}
            loading={loading}
          >
            预览数据
          </Button>,
        ];

      case 1: {
        const hasErrors = previewData?.errors && previewData.errors.length > 0;
        return [
          <Button key="back" onClick={() => setCurrentStep(0)}>
            返回
          </Button>,
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={handleImport}
            disabled={hasErrors}
            loading={loading}
          >
            确认导入
          </Button>,
        ];
      }

      default:
        return [];
    }
  };

  return (
    <Modal
      title="Excel批量导入历史要素"
      open={visible}
      onCancel={handleCancel}
      width={900}
      footer={getModalFooter()}
      destroyOnHidden
    >
      <Steps current={currentStep} items={steps} style={{ marginBottom: 24 }} />
      {renderStepContent()}
    </Modal>
  );
};
