import { Descriptions, Modal, Tag } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

export interface ElementRelationDetailModalProps {
  visible: boolean;
  record: API.ElementRelation | null;
  onClose: () => void;
}

export const ElementRelationDetailModal: React.FC<
  ElementRelationDetailModalProps
> = ({ visible, record, onClose }) => {
  if (!record) return null;

  // 获取源要素类型显示名称
  const getSourceTypeName = (sourceType: string) => {
    switch (sourceType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
        return '水系';
      case 'historical_element':
        return '历史要素';
      default:
        return sourceType;
    }
  };

  // 获取目标类型显示名称
  const getTargetTypeName = (targetType: string) => {
    switch (targetType) {
      case 'element':
        return '具体要素';
      case 'category':
        return '类别';
      default:
        return targetType;
    }
  };

  // 获取目标要素类型显示名称
  const getTargetEntityTypeName = (targetEntityType?: string) => {
    if (!targetEntityType) return '-';
    switch (targetEntityType) {
      case 'mountain':
        return '山塬';
      case 'water_system':
        return '水系';
      case 'historical_element':
        return '历史要素';
      case 'type_dict':
        return '类型字典';
      case 'region_dict':
        return '区域字典';
      default:
        return targetEntityType;
    }
  };

  // 获取状态标签
  const getStatusTag = (status: number) => {
    return status === 1 ? (
      <Tag color="green">启用</Tag>
    ) : (
      <Tag color="red">禁用</Tag>
    );
  };

  return (
    <Modal
      title={`要素关联详情 - ID: ${record.id}`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      destroyOnClose
    >
      <Descriptions
        bordered
        column={2}
        size="middle"
        labelStyle={{ width: '140px', fontWeight: 'bold' }}
      >
        <Descriptions.Item label="关联ID" span={1}>
          {record.id}
        </Descriptions.Item>
        <Descriptions.Item label="状态" span={1}>
          {getStatusTag(record.status)}
        </Descriptions.Item>

        <Descriptions.Item label="关系类型" span={1}>
          {record.relationDict?.relationName ? (
            <Tag color="blue">{record.relationDict.relationName}</Tag>
          ) : (
            <Tag color="default">未设置</Tag>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="关系编码" span={1}>
          {record.relationDict?.relationCode || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="源要素类型" span={1}>
          <Tag color="orange">{getSourceTypeName(record.sourceType)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="源要素ID" span={1}>
          {record.sourceId}
        </Descriptions.Item>

        <Descriptions.Item label="目标类型" span={1}>
          <Tag color="purple">{getTargetTypeName(record.targetType)}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="目标要素类型" span={1}>
          {record.targetEntityType ? (
            <Tag color="cyan">
              {getTargetEntityTypeName(record.targetEntityType)}
            </Tag>
          ) : (
            '-'
          )}
        </Descriptions.Item>

        <Descriptions.Item label="目标要素ID" span={1}>
          {record.targetId}
        </Descriptions.Item>
        <Descriptions.Item label="关联方向" span={1}>
          {record.direction ? (
            <Tag color="geekblue">{record.direction}</Tag>
          ) : (
            '-'
          )}
        </Descriptions.Item>

        <Descriptions.Item label="词条" span={1}>
          {record.term || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="排序号" span={1}>
          {record.sort !== undefined ? record.sort : '-'}
        </Descriptions.Item>

        <Descriptions.Item label="父级关系ID" span={1}>
          {record.parentRelationshipId || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="关系字典ID" span={1}>
          {record.relationDictId || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="创建时间" span={1}>
          {record.createdAt
            ? dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={1}>
          {record.updatedAt
            ? dayjs(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
        </Descriptions.Item>

        {record.record && (
          <Descriptions.Item label="记载内容" span={2}>
            <div
              style={{
                maxHeight: '200px',
                overflowY: 'auto',
                padding: '8px',
                backgroundColor: '#fafafa',
                borderRadius: '4px',
                whiteSpace: 'pre-wrap',
              }}
            >
              {record.record}
            </div>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};

export default ElementRelationDetailModal;
