import DictSelect from '@/components/DictSelect';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Card, Col, Input, Row, Space } from 'antd';
import React from 'react';

export interface HistoricalElementToolbarProps {
  searchKeyword: string;
  regionFilter: number | undefined;
  typeFilter: number | undefined;
  onSearchKeywordChange: (value: string) => void;
  onSearch: (value: string) => void;
  onRegionFilterChange: (value: number | undefined) => void;
  onTypeFilterChange: (value: number | undefined) => void;
  onRefresh: () => void;
  onReset: () => void;
  /** 是否显示区域选择器，默认为false（因为现在使用左侧树形选择器） */
  showRegionFilter?: boolean;
}

export const HistoricalElementToolbar: React.FC<
  HistoricalElementToolbarProps
> = ({
  searchKeyword,
  regionFilter,
  typeFilter,
  onSearchKeywordChange,
  onSearch,
  onRegionFilterChange,
  onTypeFilterChange,
  onRefresh,
  onReset,
  showRegionFilter = false,
}) => {
  return (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={showRegionFilter ? 6 : 8}>
          <Input.Search
            placeholder="搜索历史要素名称"
            allowClear
            value={searchKeyword}
            onChange={(e) => onSearchKeywordChange(e.target.value)}
            onSearch={onSearch}
            enterButton={<SearchOutlined />}
          />
        </Col>
        {showRegionFilter && (
          <Col span={4}>
            <DictSelect.DictTreeSelect
              type="region"
              placeholder="选择区域"
              allowClear
              value={regionFilter}
              onChange={onRegionFilterChange}
              style={{ width: '100%' }}
            />
          </Col>
        )}
        <Col span={showRegionFilter ? 4 : 6}>
          <DictSelect.DictTreeSelect
            type="type"
            placeholder="选择类型"
            allowClear
            value={typeFilter}
            onChange={onTypeFilterChange}
            style={{ width: '100%' }}
          />
        </Col>
        <Col span={showRegionFilter ? 6 : 10}>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={onRefresh}>
              刷新
            </Button>
            <Button onClick={onReset}>重置</Button>
          </Space>
        </Col>
      </Row>
    </Card>
  );
};
