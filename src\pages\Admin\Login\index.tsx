import { login } from '@/services/auth';
import { setToken } from '@/utils/auth';
import { wait } from '@/utils/utils';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import { Button, Card, Form, Input, Typography, message } from 'antd';
import React, { useState } from 'react';

const { Title } = Typography;

const AdminLogin: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { setInitialState } = useModel('@@initialState');

  const onFinish = async (values: { username: string; password: string }) => {
    setLoading(true);

    try {
      // 登录前先清除所有本地存储，确保没有残留状态
      localStorage.clear();
      sessionStorage.clear();

      // 调用登录接口
      const response = await login(values);

      if (response.errCode === 0 && response.data) {
        console.log('登录成功，用户信息:', response.data.user);

        // 保存token
        setToken(response.data.token);

        // 登录成功，更新全局状态
        await setInitialState({
          name: '智慧营建系统',
          currentUser: response.data.user,
          isAdmin: response.data.user.role === 'admin',
        });

        await wait(200);
        message.success('登录成功！');
        history.push('/admin/dashboard');
      } else {
        message.error(response.msg || '登录失败');
      }
    } catch (error: any) {
      console.error('登录失败:', error);
      message.error(error?.response?.data?.message || '登录失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Card className="login-form">
        <div className="login-logo">
          <img src="/logo.png" alt="智慧营建系统" />
        </div>
        <Title level={2} className="login-title" style={{ marginBottom: 24 }}>
          智慧营建管理系统
        </Title>

        <Form name="login" onFinish={onFinish} autoComplete="off" size="large">
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, max: 50, message: '用户名长度为3-50字符!' },
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, max: 50, message: '密码长度为6-50字符!' },
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%' }}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: 'center', marginTop: 24 }}>
          <Button type="link" onClick={() => history.push('/')}>
            返回首页
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default AdminLogin;
