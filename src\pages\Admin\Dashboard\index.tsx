// 移除模拟数据导入，使用真实API数据
import type { DashboardOverviewData } from '@/services/dashboard';
import { getDashboardOverview } from '@/services/dashboard';
import {
  BookOutlined,
  EnvironmentOutlined,
  FileImageOutlined,
  GlobalOutlined,
  HistoryOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { history } from '@umijs/max';
import {
  Avatar,
  Card,
  Col,
  List,
  Row,
  Statistic,
  Typography,
  message,
} from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useEffect, useMemo, useState } from 'react';

const { Title } = Typography;

const AdminDashboard: React.FC = () => {
  const [overview, setOverview] = useState<DashboardOverviewData | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await getDashboardOverview();
        if (res.errCode === 0 && res.data) {
          setOverview(res.data);
        } else {
          message.error(res.msg || '获取仪表盘概览失败');
        }
      } catch (e: any) {
        message.error(e?.message || '获取仪表盘概览失败');
      } finally {
      }
    };
    fetchData();
  }, []);

  const recentData = useMemo(() => {
    const list: Array<{
      title: string;
      type: string;
      time: string;
      avatar: string;
    }> = [];
    if (overview?.recentData?.mountains) {
      overview.recentData.mountains.forEach((m) =>
        list.push({
          title: m.name,
          type: '山塬',
          time: (m.createdAt || '').slice(0, 10),
          avatar: '⛰️',
        }),
      );
    }
    if (overview?.recentData?.waterSystems) {
      overview.recentData.waterSystems.forEach((w) =>
        list.push({
          title: w.name,
          type: '水系',
          time: (w.createdAt || '').slice(0, 10),
          avatar: '🌊',
        }),
      );
    }
    if (overview?.recentData?.historicalElements) {
      overview.recentData.historicalElements.forEach((h) =>
        list.push({
          title: h.name,
          type: '历史要素',
          time: (h.createdAt || '').slice(0, 10),
          avatar: '🏛️',
        }),
      );
    }
    return list.slice(0, 8);
  }, [overview]);

  // 饼图配置
  const pieOption = {
    title: {
      text: '要素类型分布',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      textStyle: {
        fontSize: 12,
      },
    },
    series: [
      {
        name: '要素数量',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { name: '山塬', value: overview?.statistics?.mountain || 0 },
          { name: '水系', value: overview?.statistics?.waterSystem || 0 },
          {
            name: '历史要素',
            value: overview?.statistics?.historicalElement || 0,
          },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // 柱状图配置
  const barOption = {
    title: {
      text: '区域分布统计',
      left: 'center',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: overview?.regionDistribution?.map((r) => r.region) || ['—'],
      axisLabel: {
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10,
      },
    },
    series: [
      {
        name: '要素数量',
        type: 'bar',
        data: overview?.regionDistribution?.map((r) => r.total) || [0],
        itemStyle: {
          color: '#1890ff',
        },
      },
    ],
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        仪表盘
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="山塬总数"
              value={overview?.statistics?.mountain || 0}
              prefix={<EnvironmentOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="水系总数"
              value={overview?.statistics?.waterSystem || 0}
              prefix={<GlobalOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="历史要素总数"
              value={overview?.statistics?.historicalElement || 0}
              prefix={<HistoryOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="用户总数"
              value={overview?.statistics?.user || 0}
              prefix={<UserOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表和列表 */}
      <Row gutter={16}>
        <Col span={8}>
          <Card title="要素类型分布" style={{ height: 400 }}>
            <ReactECharts option={pieOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="区域分布统计" style={{ height: 400 }}>
            <ReactECharts option={barOption} style={{ height: '300px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="最近更新" style={{ height: 400 }}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              <List
                itemLayout="horizontal"
                dataSource={recentData}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar>{item.avatar}</Avatar>}
                      title={item.title}
                      description={`${item.type} · ${item.time}`}
                    />
                  </List.Item>
                )}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快捷操作 */}
      <Row gutter={16} style={{ marginTop: 24 }}>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/mountain');
            }}
          >
            <EnvironmentOutlined
              style={{ fontSize: 48, color: '#52c41a', marginBottom: 16 }}
            />
            <div>管理山塬数据</div>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/upload');
            }}
          >
            <FileImageOutlined
              style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }}
            />
            <div>上传资源文件</div>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            hoverable
            style={{ textAlign: 'center', cursor: 'pointer' }}
            onClick={() => {
              history.push('/admin/dictionary');
            }}
          >
            <BookOutlined
              style={{ fontSize: 48, color: '#fa8c16', marginBottom: 16 }}
            />
            <div>管理字典数据</div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AdminDashboard;
