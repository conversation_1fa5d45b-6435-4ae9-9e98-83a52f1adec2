import DictSelect from '@/components/DictSelect';
import {
  EnvironmentOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  NumberOutlined,
  TagOutlined,
} from '@ant-design/icons';
import {
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Space,
  Tooltip,
} from 'antd';
import React, { useEffect } from 'react';
import styles from './MountainForm.module.less';

const { TextArea } = Input;

export interface MountainFormProps {
  visible: boolean;
  loading: boolean;
  editingItem: API.Mountain | null;
  onOk: (values: any) => void;
  onCancel: () => void;
}

export const MountainForm: React.FC<MountainFormProps> = ({
  visible,
  loading,
  editingItem,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm();

  // 当编辑项变化时，更新表单数据
  useEffect(() => {
    if (visible && editingItem) {
      form.setFieldsValue(editingItem);
    } else if (visible && !editingItem) {
      form.resetFields();
    }
  }, [visible, editingItem, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <Space className={styles.modalTitle}>
          <EnvironmentOutlined />
          {editingItem ? '编辑山塬' : '添加山塬'}
        </Space>
      }
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={900}
      confirmLoading={loading}
      destroyOnHidden
      okText="确定"
      cancelText="取消"
      styles={{
        body: { padding: '20px 24px' },
      }}
    >
      <Form form={form} layout="vertical" className={styles.formContainer}>
        {/* 基本信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <InfoCircleOutlined />
              基本信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="name"
                label={
                  <Space className={styles.fieldLabel}>
                    <TagOutlined />
                    名称
                  </Space>
                }
                rules={[{ required: true, message: '请输入山塬名称' }]}
                className={styles.formItem}
              >
                <Input placeholder="请输入山塬名称" showCount maxLength={100} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label={
                  <Space className={styles.fieldLabel}>
                    <NumberOutlined />
                    编号
                  </Space>
                }
                rules={[{ required: true, message: '请输入山塬编号' }]}
                className={styles.formItem}
              >
                <Input placeholder="请输入山塬编号" showCount maxLength={50} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="typeDictId"
                label={<span className={styles.fieldLabel}>类型</span>}
                rules={[{ required: false, message: '请选择山塬类型' }]}
                className={styles.formItem}
              >
                <DictSelect.DictTreeSelect
                  type="type"
                  placeholder="请选择山塬类型"
                  allowClear
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="regionDictId"
                label={<span className={styles.fieldLabel}>所属区域</span>}
                rules={[{ required: true, message: '请选择所属区域' }]}
                className={styles.formItem}
              >
                <DictSelect.DictTreeSelect
                  type="region"
                  placeholder="请选择所属区域"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 位置信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <EnvironmentOutlined />
              位置信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Row gutter={16} className={styles.twoColumnRow}>
            <Col span={12}>
              <Form.Item
                name="longitude"
                label={
                  <Space className={styles.fieldLabel}>
                    经度
                    <Tooltip title="经度范围：-180 到 180，支持6位小数">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { required: false, message: '请输入经度' },
                  {
                    type: 'number',
                    min: -180,
                    max: 180,
                    message: '经度范围应在-180到180之间',
                  },
                ]}
                className={styles.formItem}
              >
                <InputNumber
                  className={styles.coordinateInput}
                  style={{ width: '100%' }}
                  placeholder="请输入经度（如：116.397428）"
                  precision={6}
                  min={-180}
                  max={180}
                  step={0.000001}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="latitude"
                label={
                  <Space className={styles.fieldLabel}>
                    纬度
                    <Tooltip title="纬度范围：-90 到 90，支持6位小数">
                      <InfoCircleOutlined className={styles.tooltipIcon} />
                    </Tooltip>
                  </Space>
                }
                rules={[
                  { required: false, message: '请输入纬度' },
                  {
                    type: 'number',
                    min: -90,
                    max: 90,
                    message: '纬度范围应在-90到90之间',
                  },
                ]}
                className={styles.formItem}
              >
                <InputNumber
                  className={styles.coordinateInput}
                  style={{ width: '100%' }}
                  placeholder="请输入纬度（如：39.909200）"
                  precision={6}
                  min={-90}
                  max={90}
                  step={0.000001}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="height"
            label={
              <Space className={styles.fieldLabel}>
                <NumberOutlined />
                高度(米)
                <Tooltip title="请输入山塬高度，单位：米">
                  <InfoCircleOutlined className={styles.tooltipIcon} />
                </Tooltip>
              </Space>
            }
            rules={[{ required: true, message: '请输入高度' }]}
            className={styles.formItem}
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入高度"
              min={0}
              max={10000}
            />
          </Form.Item>

          <Form.Item
            name="locationDescription"
            label={<span className={styles.fieldLabel}>位置描述</span>}
            rules={[{ required: false, message: '请输入位置描述' }]}
            className={styles.formItem}
          >
            <Input
              placeholder="请输入详细的位置描述，如：北京市朝阳区某某街道"
              showCount
              maxLength={200}
            />
          </Form.Item>
        </Card>

        {/* 详细信息 */}
        <Card
          size="small"
          title={
            <Space className={styles.sectionTitle}>
              <FileTextOutlined />
              详细信息
            </Space>
          }
          className={styles.sectionCard}
        >
          <Form.Item
            name="historicalRecords"
            label={<span className={styles.fieldLabel}>历史记载</span>}
            rules={[{ required: false, message: '请输入历史记载' }]}
            className={styles.formItem}
          >
            <TextArea
              className={styles.textArea}
              rows={6}
              placeholder="请输入历史记载、文献资料、考古发现等相关信息..."
              showCount
              maxLength={2000}
            />
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};
