import { getPhotoStatistics } from '@/services/upload';
import { Card, Col, Row, Spin, Statistic } from 'antd';
import React, { useEffect, useState } from 'react';

interface PhotoStatisticsProps {
  onRefresh?: () => void;
  statistics?: API.PhotoStatistics | null; // 外部传入的统计数据
}

/**
 * 照片统计组件
 */
const PhotoStatisticsComponent: React.FC<PhotoStatisticsProps> = ({
  onRefresh,
  statistics: externalStatistics,
}) => {
  const [internalStatistics, setInternalStatistics] =
    useState<API.PhotoStatistics | null>(null);
  const [loading, setLoading] = useState(false);

  // 使用外部传入的统计数据，如果没有则使用内部状态
  const statistics = externalStatistics || internalStatistics;

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const response = await getPhotoStatistics();
      if (response.errCode === 0 && response.data) {
        setInternalStatistics(response.data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 只有在没有外部统计数据时才加载内部数据
    if (!externalStatistics) {
      loadStatistics();
    }
  }, [externalStatistics]);

  // 当外部触发刷新时重新加载
  useEffect(() => {
    if (onRefresh && !externalStatistics) {
      loadStatistics();
    }
  }, [onRefresh, externalStatistics]);

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  return (
    <Row gutter={16}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总照片数"
            value={statistics?.total || 0}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="关联山塬"
            value={statistics?.mountainPhotos || 0}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="关联水系"
            value={statistics?.waterSystemPhotos || 0}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="关联历史要素"
            value={statistics?.historicalElementPhotos || 0}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default PhotoStatisticsComponent;
