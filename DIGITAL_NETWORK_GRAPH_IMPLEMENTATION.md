# 门户数字化界面关系网络图实现说明

## 功能概述

在门户数字化界面（`/digital`页面）下方成功添加了关系网络图组件，复用了管理端已经实现的`NetworkGraphWithControls`组件，提供完整的筛选和控制功能。

## 实现内容

### 1. 组件集成
- 复用了管理端的 `NetworkGraphWithControls` 组件
- 导入路径：`@/components/NetworkGraph/NetworkGraphWithControls`
- 替换了简单的 `NetworkGraphSection` 组件

### 2. 数据加载功能
在 `useDigitalStatistics` Hook 中添加了网络图数据的加载功能：

#### 新增状态管理
- `networkData`: 网络图数据状态
- `loading.network`: 网络图加载状态
- `error.network`: 网络图错误状态

#### 新增方法
- `loadNetworkData()`: 加载网络图数据
- 集成到 `loadAllData()` 和 `refreshData()` 中

### 3. API接口调用
- 使用 `getPublicNetworkGraphData()` 接口获取公开的网络图数据
- 接口路径：`/openapi/relationship/network-graph`
- 支持容错处理，API失败时使用模拟数据

### 4. 筛选功能
继承了管理端的完整筛选功能：

#### 基础筛选
- **要素类型筛选**: 山塬、水系、历史要素等
- **关系类型筛选**: 地理关联、历史关联、空间关联等
- **关联方向筛选**: 单向、双向、全部

#### 高级筛选
- **最小连接数**: 过滤连接数少于指定值的节点
- **节点大小调节**: 动态调整节点显示大小
- **连线粗细调节**: 动态调整连线显示粗细

#### 交互筛选
- **点击节点**: 显示节点详情信息
- **点击连线**: 快速筛选该关系类型
- **工具栏控制**: 显示/隐藏筛选条件、全屏显示等

### 5. 控制功能
- **筛选条件显示/隐藏**: 可折叠的筛选栏
- **刷新数据**: 重新加载网络图数据
- **设置面板**: 图表高度、标签显示等配置
- **全屏显示**: 支持全屏查看网络图
- **导出功能**: 支持图片导出（预留接口）

### 6. 模拟数据
为了演示功能，添加了模拟数据：
- 包含山塬、水系、历史要素等不同类型节点
- 包含地理关联、历史关联、空间关联等不同关系
- 支持双向和单向关系展示

## 技术实现

### 文件修改

#### 主要修改文件
1. `src/pages/Digital/index.tsx`: 主页面组件
   - 导入 `NetworkGraphWithControls` 组件
   - 添加网络图渲染区域
   - 配置刷新回调

2. `src/pages/Digital/hooks/useDigitalStatistics.ts`: 数据管理Hook
   - 添加网络图数据状态管理
   - 实现 `loadNetworkData` 方法
   - 集成到统一的数据加载流程
   - 添加容错处理和模拟数据

#### 删除文件
- `src/pages/Digital/components/NetworkGraphSection.tsx`: 简单版本组件（已删除）

### 数据流程
1. 页面加载时自动调用 `loadAllData()` 加载所有数据
2. 网络图数据通过 `getPublicNetworkGraphData()` API获取
3. 如果API失败，自动使用模拟数据确保功能可用
4. 数据传递给 `NetworkGraphWithControls` 组件进行渲染
5. 用户可通过筛选条件和控制面板进行交互

### 关键特性
- **完整的筛选功能**: 支持多维度数据筛选
- **交互式操作**: 点击节点和连线进行快速筛选
- **响应式布局**: 适配不同屏幕尺寸
- **容错处理**: API失败时使用模拟数据
- **性能优化**: 使用React.memo防止不必要的重渲染

## 使用说明

1. 访问门户数字化界面：`http://localhost:8000/digital`
2. 页面下方会显示"要素关系网络图"卡片
3. 支持的操作：
   - 使用筛选条件过滤显示的节点和关系
   - 点击节点查看详细信息
   - 点击连线快速筛选关系类型
   - 使用工具栏控制显示选项
   - 拖拽和缩放图表进行查看
   - 全屏模式查看大型网络图

## 注意事项

1. **API依赖**: 当前使用模拟数据，实际部署时需要确保后端API正常工作
2. **性能考虑**: 大量节点时可能影响渲染性能，建议使用筛选功能
3. **浏览器兼容**: 需要支持现代浏览器的Canvas和SVG功能
4. **数据格式**: 确保API返回的数据格式符合`API.NetworkGraphData`接口定义

## 后续优化

1. **数据缓存**: 添加网络图数据的本地缓存机制
2. **性能优化**: 大数据量时的虚拟化渲染
3. **更多筛选**: 添加时间范围、区域等更多筛选维度
4. **导出功能**: 完善图片和数据导出功能
5. **主题定制**: 支持不同的图表主题和配色方案
