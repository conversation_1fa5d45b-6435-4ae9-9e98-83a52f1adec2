import { request } from '@umijs/max';

/**
 * 创建水系
 */
export async function createWaterSystem(
  params: API.CreateWaterSystemParams,
): Promise<API.ResType<API.WaterSystem>> {
  return request('/admin/water-system', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新水系
 */
export async function updateWaterSystem(
  id: number,
  params: API.UpdateWaterSystemParams,
): Promise<API.ResType<API.WaterSystem>> {
  return request(`/admin/water-system/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除水系
 */
export async function deleteWaterSystem(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/water-system/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取水系详情
 */
export async function getWaterSystemDetail(
  id: number,
): Promise<API.ResType<API.WaterSystem>> {
  return request(`/admin/water-system/${id}`, {
    method: 'GET',
  });
}

/**
 * 获取水系列表
 */
export async function getWaterSystemList(
  params?: API.GetWaterSystemListParams,
): Promise<API.ResType<API.WaterSystemListResponse>> {
  console.log('🔗 getWaterSystemList called with params:', params);

  const queryParams = new URLSearchParams();
  if (params?.page) queryParams.append('page', params.page.toString());
  if (params?.pageSize)
    queryParams.append('pageSize', params.pageSize.toString());
  if (params?.keyword) queryParams.append('keyword', params.keyword);
  if (params?.regionId) {
    console.log('➕ Adding regionId to query:', params.regionId);
    queryParams.append('regionId', params.regionId.toString());
  } else {
    console.log('❌ regionId is undefined, not adding to query');
  }
  if (params?.typeId) queryParams.append('typeId', params.typeId.toString());

  const url = `/admin/water-system${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  console.log('🚀 Making request to URL:', url);

  return request(url, {
    method: 'GET',
  });
}

/**
 * 批量删除水系
 */
export async function batchDeleteWaterSystems(
  ids: number[],
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/water-system/batch-delete', {
    method: 'DELETE',
    data: { ids },
  });
}

/**
 * 获取Excel导入模板下载信息
 */
export async function getWaterSystemImportTemplateInfo(): Promise<
  API.ResType<API.TemplateDownloadResponse>
> {
  return request('/admin/water-system/template/download', {
    method: 'GET',
  });
}

/**
 * 预览Excel文件
 */
export async function previewWaterSystemExcel(
  file: File,
): Promise<API.ResType<any[]>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/water-system/import/preview', {
    method: 'POST',
    data: formData,
  });
}

/**
 * Excel文件导入
 */
export async function importWaterSystemExcelFile(
  file: File,
): Promise<API.ResType<API.ExcelImportResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/water-system/import/execute', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 批量导入水系（保留原有JSON导入功能）
 */
export async function batchImportWaterSystems(
  params: API.BatchImportParams,
): Promise<API.ResType<{ message: string }>> {
  return request('/admin/water-system/batch-import', {
    method: 'POST',
    data: params,
  });
}

/**
 * 获取水系统计
 */
export async function getWaterSystemStatistics(params?: {
  regionId?: number;
}): Promise<API.ResType<API.WaterSystemStatistics>> {
  console.log('📊 getWaterSystemStatistics called with params:', params);

  const queryParams = new URLSearchParams();
  if (params?.regionId) {
    console.log('➕ Adding regionId to statistics query:', params.regionId);
    queryParams.append('regionId', params.regionId.toString());
  } else {
    console.log('❌ regionId is undefined, not adding to statistics query');
  }

  const url = `/admin/water-system/statistics/overview${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  console.log('🚀 Making statistics request to URL:', url);

  return request(url, {
    method: 'GET',
  });
}

/**
 * 根据类型获取水系
 */
export async function getWaterSystemsByType(
  typeId: number,
): Promise<API.ResType<API.WaterSystem[]>> {
  return request(`/admin/water-system/by-type/${typeId}`, {
    method: 'GET',
  });
}

/**
 * 根据区域获取水系
 */
export async function getWaterSystemsByRegion(
  regionId: number,
): Promise<API.ResType<API.WaterSystem[]>> {
  return request(`/admin/water-system/by-region/${regionId}`, {
    method: 'GET',
  });
}

/**
 * 根据长度/面积范围查询水系
 */
export async function getWaterSystemsByLengthArea(params: {
  minLength?: number;
  maxLength?: number;
}): Promise<API.ResType<API.WaterSystem[]>> {
  const queryParams = new URLSearchParams();
  if (params.minLength)
    queryParams.append('minLength', params.minLength.toString());
  if (params.maxLength)
    queryParams.append('maxLength', params.maxLength.toString());

  const url = `/admin/water-system/by-length-area${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;

  return request(url, {
    method: 'GET',
  });
}

/**
 * 获取所有水系（用于下拉选择）
 */
export async function getAllWaterSystems(): Promise<
  API.ResType<API.WaterSystem[]>
> {
  return request('/openapi/water-system/all', {
    method: 'GET',
  });
}

// ==================== 公开接口（门户） ====================

export async function getPublicWaterSystemList(
  params?: API.GetWaterSystemListParams,
): Promise<API.ResType<API.WaterSystemListResponse>> {
  const query = new URLSearchParams();
  if (params?.page) query.append('page', String(params.page));
  if (params?.pageSize) query.append('pageSize', String(params.pageSize));
  if (params?.keyword) query.append('keyword', params.keyword);
  if (params?.regionId) query.append('regionId', String(params.regionId));
  if (params?.typeId) query.append('typeId', String(params.typeId));
  const url = `/openapi/water-system/list${
    query.toString() ? `?${query.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getPublicWaterSystemDetail(
  id: number,
): Promise<API.ResType<API.WaterSystem>> {
  return request(`/openapi/water-system/${id}`, { method: 'GET' });
}

export async function getPublicWaterSystemPhotos(
  id: number,
): Promise<API.ResType<Array<{ id: number; name: string; url: string }>>> {
  return request(`/openapi/water-system/${id}/photos`, { method: 'GET' });
}

/**
 * 上传水系照片
 */
export async function uploadWaterSystemPhoto(
  waterSystemId: number,
  file: File,
): Promise<API.ResType<{ id: number; name: string; url: string }>> {
  const formData = new FormData();
  formData.append('file', file);

  return request(`/admin/water-system/${waterSystemId}/photo`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 删除水系照片
 */
export async function deleteWaterSystemPhoto(
  waterSystemId: number,
  photoId: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/water-system/${waterSystemId}/photo/${photoId}`, {
    method: 'DELETE',
  });
}
