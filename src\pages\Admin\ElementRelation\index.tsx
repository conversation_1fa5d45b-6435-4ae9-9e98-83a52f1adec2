import NetworkGraphStatistics from '@/components/NetworkGraph/NetworkGraphStatistics';
import NetworkGraphWithControls from '@/components/NetworkGraph/NetworkGraphWithControls';
import {
  createElementRelation,
  getElementRelationDetail,
  getElementRelationStatistics,
  getNetworkGraphData,
  updateElementRelation,
} from '@/services/elementRelation';
import {
  BarChartOutlined,
  EditOutlined,
  EyeOutlined,
  ImportOutlined,
  ShareAltOutlined,
  TableOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Card, message, Modal, Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import ElementRelationDetailModal from './components/ElementRelationDetailModal';
import ElementRelationForm from './components/ElementRelationForm';
import ElementRelationTable from './components/ElementRelationTable';
import RelationshipImport from './components/RelationshipImport';

const ElementRelationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('list');
  const [modalVisible, setModalVisible] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>(
    'create',
  );
  const [currentRecord, setCurrentRecord] =
    useState<API.ElementRelation | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [networkData, setNetworkData] = useState<API.NetworkGraphData | null>(
    null,
  );
  const [statistics, setStatistics] =
    useState<API.ElementRelationStatistics | null>(null);
  const [networkLoading, setNetworkLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [formRef, setFormRef] = useState<any>(null);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [detailRecord, setDetailRecord] = useState<API.ElementRelation | null>(
    null,
  );

  // 加载网络图数据
  const loadNetworkData = async () => {
    setNetworkLoading(true);
    try {
      const response = await getNetworkGraphData();
      if (response.errCode === 0) {
        setNetworkData(response.data || null);
      } else {
        message.error(response.msg || '获取网络图数据失败');
      }
    } catch (error) {
      message.error('获取网络图数据失败');
      console.error('获取网络图数据失败:', error);
    } finally {
      setNetworkLoading(false);
    }
  };

  // 加载统计数据
  const loadStatistics = async () => {
    setStatisticsLoading(true);
    try {
      const response = await getElementRelationStatistics();
      if (response.errCode === 0) {
        setStatistics(response.data || null);
      } else {
        message.error(response.msg || '获取统计数据失败');
      }
    } catch (error) {
      message.error('获取统计数据失败');
      console.error('获取统计数据失败:', error);
    } finally {
      setStatisticsLoading(false);
    }
  };

  // 创建记录
  const handleCreate = () => {
    setModalMode('create');
    setCurrentRecord(null);
    setModalVisible(true);
  };

  // 编辑记录
  const handleEdit = async (record: API.ElementRelation) => {
    try {
      const response = await getElementRelationDetail(record.id);
      if (response.errCode === 0) {
        setModalMode('edit');
        setCurrentRecord(response.data || null);
        setModalVisible(true);
      } else {
        message.error(response.msg || '获取详情失败');
      }
    } catch (error) {
      message.error('获取详情失败');
      console.error('获取要素关联详情失败:', error);
    }
  };

  // 查看记录
  const handleView = async (record: API.ElementRelation) => {
    try {
      const response = await getElementRelationDetail(record.id);
      if (response.errCode === 0) {
        setDetailRecord(response.data || null);
        setDetailModalVisible(true);
      } else {
        message.error(response.msg || '获取详情失败');
      }
    } catch (error) {
      message.error('获取详情失败');
      console.error('获取要素关联详情失败:', error);
    }
  };

  // 保存记录
  const handleSave = async () => {
    if (!formRef) return;

    try {
      setConfirmLoading(true);
      const values = await formRef.validateFields();

      let response;
      if (modalMode === 'create') {
        response = await createElementRelation(values);
      } else {
        response = await updateElementRelation(currentRecord!.id, values);
      }

      if (response.errCode === 0) {
        message.success(modalMode === 'create' ? '创建成功' : '更新成功');
        setModalVisible(false);
        setRefreshTrigger((prev) => prev + 1);
        // 刷新网络图和统计数据
        if (activeTab === 'network') {
          loadNetworkData();
        }
        if (activeTab === 'statistics') {
          loadStatistics();
        }
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error: any) {
      if (error?.errorFields) {
        message.error('请检查表单填写');
      } else {
        message.error('保存失败');
        console.error('保存要素关联失败:', error);
      }
    } finally {
      setConfirmLoading(false);
    }
  };

  // 处理节点点击
  const handleNodeClick = (nodeData: any) => {
    console.log('节点点击:', nodeData);
    // 可以在这里实现节点点击后的逻辑，比如显示详情
  };

  // 处理连线点击
  const handleLinkClick = (linkData: any) => {
    console.log('连线点击:', linkData);
    // 可以在这里实现连线点击后的逻辑，比如显示关系详情
  };

  // 导出网络图
  const handleExportNetwork = () => {
    message.info('导出功能开发中...');
  };

  // 导入成功回调
  const handleImportSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    // 刷新网络图和统计数据
    if (activeTab === 'network') {
      loadNetworkData();
    }
    if (activeTab === 'statistics') {
      loadStatistics();
    }
    message.success('导入成功，数据已刷新');
  };

  // 标签页配置
  const tabItems = [
    {
      key: 'list',
      label: (
        <span>
          <TableOutlined />
          关联列表
        </span>
      ),
      children: (
        <ElementRelationTable
          onEdit={handleEdit}
          onView={handleView}
          onCreate={handleCreate}
          refreshTrigger={refreshTrigger}
        />
      ),
    },
    {
      key: 'network',
      label: (
        <span>
          <ShareAltOutlined />
          关系网络图
        </span>
      ),
      children: (
        <NetworkGraphWithControls
          data={networkData}
          loading={networkLoading}
          title="要素关系网络图"
          onRefresh={loadNetworkData}
          onExport={handleExportNetwork}
          onNodeClick={handleNodeClick}
          onLinkClick={handleLinkClick}
        />
      ),
    },
    {
      key: 'statistics',
      label: (
        <span>
          <BarChartOutlined />
          统计分析
        </span>
      ),
      children: (
        <Card>
          <NetworkGraphStatistics
            statistics={statistics}
            loading={statisticsLoading}
          />
        </Card>
      ),
    },
    {
      key: 'import',
      label: (
        <span>
          <ImportOutlined />
          批量导入
        </span>
      ),
      children: <RelationshipImport onImportSuccess={handleImportSuccess} />,
    },
  ];

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    if (key === 'network' && !networkData) {
      loadNetworkData();
    }
    if (key === 'statistics' && !statistics) {
      loadStatistics();
    }
  };

  // 初始化
  useEffect(() => {
    // 默认加载列表数据，其他数据按需加载
  }, []);

  return (
    <PageContainer
      title="要素关联管理"
      content="管理山塬、水系、历史要素之间的关联关系，支持可视化展示和统计分析"
    >
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        size="large"
      />

      {/* 表单弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {modalMode === 'create' && (
              <ShareAltOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'edit' && (
              <EditOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'view' && (
              <EyeOutlined style={{ color: '#1890ff' }} />
            )}
            {modalMode === 'create'
              ? '新建要素关联'
              : modalMode === 'edit'
              ? '编辑要素关联'
              : '查看要素关联'}
          </div>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setFormRef(null);
        }}
        onOk={modalMode === 'view' ? () => setModalVisible(false) : handleSave}
        okText={modalMode === 'view' ? '关闭' : '保存'}
        cancelText="取消"
        width={1000}
        confirmLoading={confirmLoading}
        destroyOnHidden
        styles={{
          body: { padding: '20px 24px' },
        }}
      >
        <ElementRelationForm
          initialValues={currentRecord || undefined}
          onFormRef={setFormRef}
          readonly={modalMode === 'view'}
        />
      </Modal>

      {/* 详情查看模态框 */}
      <ElementRelationDetailModal
        visible={detailModalVisible}
        record={detailRecord}
        onClose={() => {
          setDetailModalVisible(false);
          setDetailRecord(null);
        }}
      />
    </PageContainer>
  );
};

export default ElementRelationPage;
