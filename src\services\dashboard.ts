import { request } from '@umijs/max';

// 管理端仪表盘服务

export interface DashboardOverviewData {
  statistics: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
    user?: number;
    typeDict?: number;
    regionDict?: number;
    relationshipDict?: number;
  };
  regionDistribution: Array<{
    region: string;
    regionId: number;
    mountainCount: number;
    waterSystemCount: number;
    historicalElementCount: number;
    total: number;
  }>;
  recentData: {
    mountains: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
    waterSystems: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
    historicalElements: Array<{
      id: number;
      name: string;
      code: string;
      createdAt: string;
    }>;
  };
}

export interface DashboardStatisticsData {
  basic: {
    counts: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
    };
    regionStats: Array<{
      region: string;
      regionId: number;
      mountainCount: number;
      waterSystemCount: number;
      historicalElementCount: number;
      total: number;
    }>;
    timelineData: Array<{
      year: number;
      count: number;
      elements: Array<{ id: number; name: string; type: string }>;
    }>;
  };
  detailed: {
    mountains: {
      total: number;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
    waterSystems: {
      total: number;
      totalLength: number;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
    historicalElements: {
      total: number;
      byType: Array<{ typeId: number; typeName: string; count: number }>;
      byRegion: Array<{ regionId: number; regionName: string; count: number }>;
    };
  };
  summary: {
    totalEntities: number;
    regionCoverage: number;
    timeSpan: { earliest: number; latest: number; span: number };
  };
}

export interface DashboardGrowthTrendData {
  mountains: Array<{ date: string; count: number }>;
  waterSystems: Array<{ date: string; count: number }>;
  historicalElements: Array<{ date: string; count: number }>;
}

export interface DashboardDataQualityData {
  completeness: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
  };
  missingFields: {
    mountain: Array<{ field: string; count: number }>;
    waterSystem: Array<{ field: string; count: number }>;
    historicalElement: Array<{ field: string; count: number }>;
  };
  dataDistribution: {
    byRegion: Array<{ region: string; count: number; percentage: number }>;
    byType: Array<{ type: string; count: number; percentage: number }>;
  };
}

export async function getDashboardOverview(params?: {
  regionId?: number;
}): Promise<API.ResType<DashboardOverviewData>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', String(params.regionId));
  const url = `/admin/dashboard/overview${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getDashboardStatistics(params?: {
  regionId?: number;
  startTime?: string;
  endTime?: string;
}): Promise<API.ResType<DashboardStatisticsData>> {
  const queryParams = new URLSearchParams();
  if (params?.regionId) queryParams.append('regionId', String(params.regionId));
  if (params?.startTime) queryParams.append('startTime', params.startTime);
  if (params?.endTime) queryParams.append('endTime', params.endTime);
  const url = `/admin/dashboard/statistics${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getDashboardGrowthTrend(params?: {
  period?: 'week' | 'month' | 'year';
}): Promise<API.ResType<DashboardGrowthTrendData>> {
  const queryParams = new URLSearchParams();
  if (params?.period) queryParams.append('period', params.period);
  const url = `/admin/dashboard/growth-trend${
    queryParams.toString() ? `?${queryParams.toString()}` : ''
  }`;
  return request(url, { method: 'GET' });
}

export async function getDashboardDataQuality(): Promise<
  API.ResType<DashboardDataQualityData>
> {
  return request('/admin/dashboard/data-quality', { method: 'GET' });
}
