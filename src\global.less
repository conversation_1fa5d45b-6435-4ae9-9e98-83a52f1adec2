/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  background-attachment: fixed;
  min-height: 100vh;
}

/* 全局装饰性背景纹理 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(255, 255, 255, 10%) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(255, 255, 255, 10%) 0%,
      transparent 50%
    ),
    linear-gradient(
      45deg,
      transparent 40%,
      rgba(255, 255, 255, 5%) 50%,
      transparent 60%
    );
  background-size: 400px 400px, 300px 300px, 200px 200px;
  background-position: 0 0, 100px 100px, 50px 50px;
  pointer-events: none;
  z-index: -1;
}

/* 公共网站样式 */
.public-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

.public-layout::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(255, 255, 255, 10%) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 255, 255, 10%) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 255, 255, 5%) 0%,
      transparent 50%
    );
  background-size: 600px 600px, 800px 800px, 400px 400px;
  pointer-events: none;
  z-index: 0;
}

.public-header {
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(20px) saturate(180%);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 10%), 0 2px 8px rgba(0, 0, 0, 5%),
    inset 0 1px 0 rgba(255, 255, 255, 80%);
  border-bottom: 1px solid rgba(255, 255, 255, 20%);
  top: 0;
  z-index: 1000;
  padding: 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  /* 装饰性背景元素 */
  .header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(
        135deg,
        rgba(24, 144, 255, 10%) 0%,
        rgba(82, 196, 26, 10%) 100%
      );
      animation: float-decoration 8s ease-in-out infinite;

      &.decoration-circle-1 {
        width: 60px;
        height: 60px;
        top: -30px;
        left: 10%;
        animation-delay: 0s;
      }

      &.decoration-circle-2 {
        width: 40px;
        height: 40px;
        top: -20px;
        right: 15%;
        animation-delay: 2s;
      }
    }

    .decoration-line {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        #1890ff 20%,
        #52c41a 50%,
        #fa8c16 80%,
        transparent 100%
      );
      animation: line-flow 3s ease-in-out infinite;
    }
  }
}

.public-logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
  position: relative;
  z-index: 1;

  .logo-container {
    display: flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.05);
    }
  }

  img {
    filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 30%));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;

    .logo-title {
      font-size: 18px;
      font-weight: 700;
      background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .logo-subtitle {
      font-size: 12px;
      color: #666;
      font-weight: 400;
    }
  }
}

.public-nav {
  display: flex;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.public-nav-item {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 12px 20px;
  border-radius: 16px;
  cursor: pointer;
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 10%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 20%);
  display: flex;
  align-items: center;
  gap: 8px;
  animation: nav-item-appear 0.6s ease-out both;

  .nav-icon {
    font-size: 16px;
    transition: all 0.3s ease;
  }

  .nav-label {
    font-size: 14px;
    transition: all 0.3s ease;
  }

  .nav-indicator {
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
    border-radius: 1px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
  }

  &:hover::before,
  &.active::before {
    opacity: 1;
  }

  &:hover,
  &.active {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(24, 144, 255, 30%);

    .nav-icon {
      transform: scale(1.1);
    }

    .nav-indicator {
      transform: translateX(-50%) scaleX(1);
    }
  }

  &.active .nav-indicator {
    background: linear-gradient(90deg, #52c41a 0%, #1890ff 100%);
  }
}

.header-actions {
  position: relative;
  z-index: 1;
}

.admin-entry {
  padding: 12px 24px;
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  color: white;
  border: none;
  border-radius: 16px;
  cursor: pointer;
  font-weight: 600;
  position: relative;
  z-index: 1;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 6px 20px rgba(82, 196, 26, 30%);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: -1;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 20%);
    border-radius: 50%;
    transition: all 0.4s ease;
    z-index: 0;
  }

  &:hover::before {
    opacity: 1;
  }

  &:hover::after {
    width: 100%;
    height: 100%;
  }

  &:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(82, 196, 26, 40%);
  }

  .anticon {
    font-size: 16px;
  }
}

/* 内容区域 */
.public-content {
  padding: 0; /* 首页不需要默认padding */
  min-height: calc(100vh - 64px);
  position: relative;
  z-index: 1;
}

/* 通用容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
}

/* 卡片样式 */
.content-card {
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 10%), 0 8px 16px rgba(0, 0, 0, 5%),
    inset 0 1px 0 rgba(255, 255, 255, 80%);
  border: 1px solid rgba(255, 255, 255, 20%);
  overflow: hidden;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 10%) 0%,
    rgba(255, 255, 255, 5%) 100%
  );
  pointer-events: none;
  z-index: 0;
}

// .content-card:hover {
//   transform: translateY(-8px);
//   box-shadow: 0 32px 64px rgba(0, 0, 0, 15%), 0 16px 32px rgba(0, 0, 0, 10%),
//     inset 0 1px 0 rgba(255, 255, 255, 90%);
// }

/* 地图容器 */
.map-container {
  width: 100%;
  height: 600px;
  border-radius: 20px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 20%),
    0 8px 32px rgba(0, 0, 0, 10%);
}

.map-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 10%) 0%,
    transparent 50%
  );
  pointer-events: none;
  z-index: 1;
}

/* 详情页样式 */
.detail-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 300px;
  gap: 24px;
  margin-top: 24px;
}

.detail-info {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

.detail-images {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

.detail-relations {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

/* 列表页样式 */
.list-container {
  margin-top: 0;
}

.list-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  transition: all 0.3s;
  cursor: pointer;
}

.list-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 15%);
}

/* 增强卡片样式 */
.ant-card.ant-card-hoverable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 12%) !important;
}

.ant-card {
  transition: all 0.3s ease !important;
}

/* 列表页面卡片样式优化 */
.ant-list-item {
  padding: 0 !important;
  margin-bottom: 0 !important;
}

.ant-list-grid .ant-col > .ant-list-item {
  margin-bottom: 24px;
}

/* 分页样式优化 */
.ant-pagination {
  margin-top: 32px;
  text-align: center;
}

.ant-pagination .ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination .ant-pagination-item-active {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-pagination .ant-pagination-item-active a {
  color: white;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 30%);
}

.ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 40%);
}

/* 输入框样式优化 */
.ant-input,
.ant-input-affix-wrapper {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 20%);
}

/* 标题样式优化 */
.ant-typography h1,
.ant-typography h2,
.ant-typography h3 {
  color: #333;
  font-weight: 600;
}

/* 图片样式优化 */
.ant-image {
  border-radius: 6px;
  overflow: hidden;
}

.ant-image-img {
  transition: all 0.3s ease;
}

.ant-image:hover .ant-image-img {
  transform: scale(1.02);
}

/* 详情页面响应式布局 */
@media (max-width: 1200px) {
  .detail-layout {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .detail-layout {
    grid-template-columns: 1fr;
  }
}

/* 数字化页面样式 */
.digital-filters {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.digital-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-container {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  height: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-layout {
    grid-template-columns: 1fr 1fr !important;
  }

  .content-card {
    margin: 16px !important;
  }

  .public-header {
    padding: 0 20px;
    height: 68px;

    .public-nav {
      gap: 2px;

      .public-nav-item {
        padding: 8px 12px;
        font-size: 13px;

        .nav-label {
          display: none; /* 在中等屏幕上只显示图标 */
        }
      }
    }

    .admin-entry {
      padding: 8px 16px;
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .public-header {
    flex-direction: column;
    height: auto;
    padding: 16px;

    .header-decoration {
      display: none; /* 在小屏幕上隐藏装饰元素 */
    }

    .public-logo {
      margin-bottom: 16px;

      .logo-text {
        .logo-title {
          font-size: 16px;
        }

        .logo-subtitle {
          font-size: 11px;
        }
      }
    }

    .public-nav {
      gap: 8px;
      margin-bottom: 16px;
      flex-wrap: wrap;
      justify-content: center;

      .public-nav-item {
        padding: 8px 12px;
        font-size: 12px;

        .nav-icon {
          font-size: 14px;
        }

        .nav-label {
          display: inline; /* 在小屏幕上显示标签 */
          font-size: 12px;
        }
      }
    }

    .header-actions {
      .admin-entry {
        padding: 10px 20px;
        font-size: 14px;
      }
    }
  }

  .detail-layout {
    grid-template-columns: 1fr !important;
  }

  .digital-charts {
    grid-template-columns: 1fr;
  }

  .list-container {
    grid-template-columns: 1fr;
  }

  .content-card {
    margin: 12px !important;
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .public-header {
    padding: 12px;

    .public-nav {
      .public-nav-item {
        padding: 6px 10px;

        .nav-icon {
          font-size: 12px;
        }

        .nav-label {
          font-size: 11px;
        }
      }
    }

    .header-actions {
      .admin-entry {
        padding: 8px 16px;
        font-size: 12px;
      }
    }
  }
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 10%);
  width: 400px;
  max-width: 90vw;
}

.login-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 32px;
  color: #333;
}

.login-logo {
  text-align: center;
}

.login-logo img {
  height: 80px;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.login-logo img:hover {
  transform: scale(1.05);
}

/* 用户信息组件样式 */
.user-info-trigger:hover {
  background-color: rgba(0, 0, 0, 4%);
}

.user-dropdown-trigger:hover {
  background-color: rgba(0, 0, 0, 4%);
}

/* 头部装饰动画 */
@keyframes float-decoration {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }

  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

@keyframes line-flow {
  0% {
    background-position: -100% 0;
  }

  100% {
    background-position: 100% 0;
  }
}

@keyframes nav-item-appear {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
