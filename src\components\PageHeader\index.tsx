import { getBackgroundStyle, preloadBackground } from '@/config/backgrounds';
import { Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title, Paragraph } = Typography;

export interface PageHeaderProps {
  title: string;
  description?: string;
  backgroundType: 'mountain' | 'water' | 'history' | 'digital' | 'hero';
  height?: number;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  backgroundType,
  height = 400,
  className = '',
}) => {
  const [backgroundLoaded, setBackgroundLoaded] = useState(false);
  const [backgroundStyle, setBackgroundStyle] = useState<React.CSSProperties>(
    {},
  );

  // 预加载背景图
  useEffect(() => {
    const loadBackground = async () => {
      try {
        await preloadBackground(backgroundType);
        const style = getBackgroundStyle(backgroundType);
        setBackgroundStyle(style);
        setBackgroundLoaded(true);
      } catch (error) {
        console.warn('背景图加载失败，使用备用方案:', error);
        const style = getBackgroundStyle(backgroundType, true);
        setBackgroundStyle(style);
        setBackgroundLoaded(true);
      }
    };

    loadBackground();
  }, [backgroundType]);

  const getBackgroundClass = () => {
    switch (backgroundType) {
      case 'mountain':
        return 'mountain-header-bg';
      case 'water':
        return 'water-header-bg';
      case 'history':
        return 'history-header-bg';
      case 'digital':
        return 'digital-header-bg';
      default:
        return 'default-header-bg';
    }
  };

  return (
    <div
      className={`page-header ${getBackgroundClass()} ${className} ${
        backgroundLoaded ? 'loaded' : 'loading'
      }`}
      style={{
        minHeight: height,
        ...backgroundStyle,
      }}
    >
      {/* 装饰性背景元素 */}
      <div className="header-decorations">
        <div className="decoration-particle decoration-particle-1"></div>
        <div className="decoration-particle decoration-particle-2"></div>
        <div className="decoration-particle decoration-particle-3"></div>
        <div className="decoration-line decoration-line-1"></div>
        <div className="decoration-line decoration-line-2"></div>
      </div>

      {/* 内容区域 */}
      <div className="page-header-content">
        <Title level={1} className="page-header-title">
          {title}
        </Title>
        {description && (
          <Paragraph className="page-header-description">
            {description}
          </Paragraph>
        )}
      </div>

      {/* 遮罩层 */}
      <div className="page-header-overlay"></div>
    </div>
  );
};

export default PageHeader;
