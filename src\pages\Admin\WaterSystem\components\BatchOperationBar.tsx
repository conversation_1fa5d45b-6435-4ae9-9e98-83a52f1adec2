import { DeleteOutlined } from '@ant-design/icons';
import { Button, Popconfirm, Space, Typography } from 'antd';
import React from 'react';

const { Text } = Typography;

export interface BatchOperationBarProps {
  selectedRowKeys: React.Key[];
  onBatchDelete: () => void;
  onClearSelection: () => void;
}

export const BatchOperationBar: React.FC<BatchOperationBarProps> = ({
  selectedRowKeys,
  onBatchDelete,
  onClearSelection,
}) => {
  if (selectedRowKeys.length === 0) {
    return null;
  }

  return (
    <div
      style={{
        marginBottom: 16,
        padding: '12px 16px',
        backgroundColor: '#e6f7ff',
        border: '1px solid #91d5ff',
        borderRadius: 6,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Space>
        <Text strong style={{ color: '#1890ff' }}>
          已选择 {selectedRowKeys.length} 项
        </Text>
      </Space>

      <Space>
        <Button size="small" onClick={onClearSelection}>
          取消选择
        </Button>
        <Popconfirm
          title="批量删除确认"
          description={`确定要删除选中的 ${selectedRowKeys.length} 个水系吗？删除后将无法恢复。`}
          onConfirm={onBatchDelete}
          okText="确定删除"
          cancelText="取消"
          okButtonProps={{ danger: true }}
        >
          <Button type="primary" danger size="small" icon={<DeleteOutlined />}>
            批量删除
          </Button>
        </Popconfirm>
      </Space>
    </div>
  );
};
