import {
  BarChartOutlined,
  EnvironmentOutlined,
  LineChartOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Statistic } from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useMemo } from 'react';

export interface MountainStatisticsProps {
  statistics: API.MountainStatistics | null;
  loading: boolean;
}

export const MountainStatistics: React.FC<MountainStatisticsProps> = ({
  statistics,
  loading,
}) => {
  // 生成图表配置
  const chartOptions = useMemo(() => {
    if (!statistics) return { barOption: null, heightOption: null };

    // 饼图配置 - 按高度统计
    const heightOption = {
      title: {
        text: '高度分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle',
      },
      series: [
        {
          name: '高度统计',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: statistics.byHeight.map((item, index) => ({
            value: item.count,
            name: item.range,
            itemStyle: {
              color: ['#1890ff', '#52c41a', '#fa8c16', '#722ed1', '#eb2f96'][
                index % 5
              ],
            },
          })),
        },
      ],
    };

    // 柱状图配置 - 按区域统计
    const barOption = {
      title: {
        text: '区域分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: statistics.byRegion.map((item) => item.regionName),
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: '#1890ff',
            borderRadius: [4, 4, 0, 0],
          },
          data: statistics.byRegion.map((item) => item.count),
        },
      ],
    };

    return { barOption, heightOption };
  }, [statistics]);

  if (!statistics) {
    return (
      <Card loading={loading}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          暂无统计数据
        </div>
      </Card>
    );
  }

  // 渲染统计卡片
  const renderStatisticsCards = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="山塬总数"
              value={statistics.total}
              prefix={<BarChartOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="区域数量"
              value={statistics.byRegion.length}
              prefix={<EnvironmentOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="高度分布"
              value={statistics.byHeight.length}
              prefix={<LineChartOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染图表区域
  const renderCharts = () => {
    if (!statistics || !chartOptions.barOption) return null;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.barOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card loading={loading} style={{ height: 400 }}>
            <ReactECharts
              option={chartOptions.heightOption}
              style={{ height: '350px' }}
              opts={{ renderer: 'svg' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染统计详情表格
  const renderStatisticsDetails = () => {
    if (!statistics) return null;

    return (
      <Row gutter={16}>
        <Col span={12}>
          <Card title="按区域统计" loading={loading}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {statistics.byRegion.map((item, index) => (
                <div
                  key={item.regionId}
                  style={{
                    marginBottom: 12,
                    padding: '8px 12px',
                    backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                    borderRadius: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <span style={{ color: '#666' }}>{item.regionName}</span>
                  <span
                    style={{
                      fontWeight: 'bold',
                      color: '#1890ff',
                      fontSize: '16px',
                    }}
                  >
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="按高度统计" loading={loading}>
            <div style={{ maxHeight: 300, overflowY: 'auto' }}>
              {statistics.byHeight.map((item, index) => (
                <div
                  key={item.range}
                  style={{
                    marginBottom: 12,
                    padding: '8px 12px',
                    backgroundColor: index % 2 === 0 ? '#fafafa' : '#fff',
                    borderRadius: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <span style={{ color: '#666' }}>{item.range}</span>
                  <span
                    style={{
                      fontWeight: 'bold',
                      color: '#52c41a',
                      fontSize: '16px',
                    }}
                  >
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      {renderStatisticsCards()}
      {renderCharts()}
      {renderStatisticsDetails()}
    </div>
  );
};
