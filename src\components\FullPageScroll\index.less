/* 全屏滚动重置样式 */
.fullpage-scroll-wrapper {
  position: relative;
  width: 100%;
  height: calc(100vh - 72px);
  overflow: hidden;

  /* 确保精确的高度计算 */
  box-sizing: border-box;
  margin: 0;
  padding: 0;

  /* 禁用选择和拖拽 */
  user-select: none;
  user-select: none;
  user-select: none;
  user-select: none;

  /* 重置所有子元素 */
  * {
    box-sizing: border-box;
  }
}

.fullpage-scroll-container {
  width: 100%;
  height: calc(100vh - 72px);
  transition-property: transform;
  will-change: transform;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  backface-visibility: hidden;
  perspective: 1000px;
}

.fullpage-section {
  width: 100%;
  height: calc(100vh - 72px);
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  backface-visibility: hidden;
  transform: translateZ(0);

  /* 确保子元素不会超出 */
  display: flex;
  flex-direction: column;

  /* 确保内容不会被导航遮挡 */
  padding-right: 0;
}

/* 导航指示器 */
.fullpage-nav {
  position: fixed;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 60%);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    padding: 0;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 40%);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover {
      border-color: rgba(255, 255, 255, 80%);
      transform: scale(1.2);

      &::before {
        background: rgba(255, 255, 255, 60%);
      }
    }

    &.active {
      border-color: #1890ff;
      background: rgba(24, 144, 255, 20%);

      &::before {
        background: #1890ff;
        width: 8px;
        height: 8px;
      }
    }
  }

  @media (max-width: 768px) {
    right: 20px;
    gap: 10px;

    .nav-dot {
      width: 10px;
      height: 10px;

      &::before {
        width: 4px;
        height: 4px;
      }

      &.active::before {
        width: 6px;
        height: 6px;
      }
    }
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .fullpage-scroll-wrapper {
    touch-action: none;
  }
}
