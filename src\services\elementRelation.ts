import { request } from '@umijs/max';

// ==================== 要素关联管理 API ====================

/**
 * 创建要素关联
 */
export async function createElementRelation(
  params: API.CreateElementRelationParams,
): Promise<API.ResType<API.ElementRelation>> {
  return request('/admin/relationship', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新要素关联
 */
export async function updateElementRelation(
  id: number,
  params: API.UpdateElementRelationParams,
): Promise<API.ResType<API.ElementRelation>> {
  return request(`/admin/relationship/${id}`, {
    method: 'PUT',
    data: params,
  });
}

/**
 * 删除要素关联
 */
export async function deleteElementRelation(
  id: number,
): Promise<API.ResType<{ message: string }>> {
  return request(`/admin/relationship/${id}`, {
    method: 'DELETE',
  });
}

/**
 * 获取要素关联列表
 */
export async function getElementRelationList(
  params?: API.GetElementRelationListParams,
): Promise<API.ResType<API.ElementRelationListResponse>> {
  return request('/admin/relationship', {
    method: 'GET',
    params,
  });
}

/**
 * 获取所有要素关联（不分页）
 */
export async function getAllElementRelations(
  params?: API.GetElementRelationListParams,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request('/admin/relationship/all', {
    method: 'GET',
    params,
  });
}

/**
 * 获取要素关联详情
 */
export async function getElementRelationDetail(
  id: number,
): Promise<API.ResType<API.ElementRelation>> {
  return request(`/admin/relationship/${id}`, {
    method: 'GET',
  });
}

/**
 * 批量创建要素关联
 */
export async function batchCreateElementRelation(params: {
  relations: API.CreateElementRelationParams[];
}): Promise<API.ResType<{ message: string }>> {
  return request('/admin/relationship/batch', {
    method: 'POST',
    data: params,
  });
}

/**
 * 根据要素获取关联关系
 */
export async function getElementRelationsByElement(
  elementType: string,
  elementId: number,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request(`/admin/relationship/by-element/${elementType}/${elementId}`, {
    method: 'GET',
  });
}

/**
 * 获取要素关联统计
 */
export async function getElementRelationStatistics(
  params?: API.GetElementRelationStatisticsParams,
): Promise<API.ResType<API.ElementRelationStatistics>> {
  return request('/admin/relationship/statistics/overview', {
    method: 'GET',
    params,
  });
}

/**
 * 获取网络图数据
 */
export async function getNetworkGraphData(
  params?: API.GetNetworkGraphParams,
): Promise<API.ResType<API.NetworkGraphData>> {
  return request('/admin/relationship/network-graph', {
    method: 'GET',
    params,
  });
}

/**
 * 批量更新状态
 */
export async function batchUpdateElementRelationStatus(params: {
  ids: number[];
  status: number;
}): Promise<API.ResType<{ message: string }>> {
  return request('/admin/relationship/batch-status', {
    method: 'PUT',
    data: params,
  });
}

// ==================== 公开接口 ====================

/**
 * 获取网络图数据（公开）
 */
export async function getPublicNetworkGraphData(
  params?: API.GetNetworkGraphParams,
): Promise<API.ResType<API.NetworkGraphData>> {
  return request('/openapi/relationship/network-graph', {
    method: 'GET',
    params,
  });
}

/**
 * 获取要素关联统计（公开）
 */
export async function getPublicElementRelationStatistics(
  params?: API.GetElementRelationStatisticsParams,
): Promise<API.ResType<API.ElementRelationStatistics>> {
  return request('/openapi/relationship/statistics', {
    method: 'GET',
    params,
  });
}

/**
 * 根据要素获取关联关系（公开）
 */
export async function getPublicElementRelationsByElement(
  elementType: string,
  elementId: number,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request(
    `/openapi/relationship/by-element/${elementType}/${elementId}`,
    {
      method: 'GET',
    },
  );
}

/**
 * 搜索要素关联（公开）
 */
export async function searchPublicElementRelations(
  keyword: string,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request(
    `/openapi/relationship/search?keyword=${encodeURIComponent(keyword)}`,
    {
      method: 'GET',
    },
  );
}

/**
 * 获取关系列表（公开）
 */
export async function getPublicRelationshipList(
  params?: API.GetElementRelationListParams,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request('/openapi/relationship/list', {
    method: 'GET',
    params,
  });
}

/**
 * 根据关系类型获取关联关系（公开）
 */
export async function getPublicRelationshipsByRelation(
  relationId: number,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request(`/openapi/relationship/by-relation/${relationId}`, {
    method: 'GET',
  });
}

/**
 * 根据方向获取关联关系（公开）
 */
export async function getPublicRelationshipsByDirection(
  direction: string,
): Promise<API.ResType<API.ElementRelation[]>> {
  return request(
    `/openapi/relationship/by-direction/${encodeURIComponent(direction)}`,
    {
      method: 'GET',
    },
  );
}

// ==================== 要素关联导入相关 API ====================

/**
 * 获取导入模板
 */
export async function getRelationshipImportTemplate(): Promise<
  API.ResType<API.RelationshipTemplateDownloadResponse>
> {
  return request('/admin/relationship/template/download', {
    method: 'GET',
  });
}

/**
 * 预览导入数据
 */
export async function previewRelationshipImport(
  file: File,
): Promise<API.ResType<API.RelationshipImportPreviewResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/relationship/import/preview', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 执行导入
 */
export async function executeRelationshipImport(
  file: File,
): Promise<API.ResType<API.RelationshipImportExecuteResponse>> {
  const formData = new FormData();
  formData.append('files', file);

  return request('/admin/relationship/import/execute', {
    method: 'POST',
    data: formData,
  });
}

/**
 * 批量导入要素关联
 */
export async function batchImportRelationships(params: {
  relationships: API.CreateElementRelationParams[];
}): Promise<API.ResType<API.RelationshipBatchImportResponse>> {
  return request('/admin/relationship/batch-import', {
    method: 'POST',
    data: params,
  });
}
