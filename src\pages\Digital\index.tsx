import NetworkGraphWithControls from '@/components/NetworkGraph/NetworkGraphWithControls';
import PublicLayout from '@/components/PublicLayout';
import { Typography } from 'antd';
import React, { useMemo, useState } from 'react';
import StatisticsCards from './components/StatisticsCards';
import StatisticsCharts from './components/StatisticsCharts';
import StatisticsFilters from './components/StatisticsFilters';
import useDigitalStatistics from './hooks/useDigitalStatistics';

const { Title } = Typography;

const DigitalPage: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [dateRange, setDateRange] = useState<any>(null);

  const regionId = useMemo(() => {
    if (selectedRegion === 'all') return undefined;
    const n = Number(selectedRegion);
    return Number.isFinite(n) ? n : undefined;
  }, [selectedRegion]);

  const startTime = useMemo(() => {
    return dateRange?.[0]?.toISOString();
  }, [dateRange]);

  const endTime = useMemo(() => {
    return dateRange?.[1]?.toISOString();
  }, [dateRange]);

  // 使用统计数据Hook
  const {
    basicData,
    overviewData,
    regionData,
    timelineData,
    networkData,
    loading,
    refreshData,
    loadNetworkData,
  } = useDigitalStatistics({
    regionId,
    startTime,
    endTime,
    autoLoad: true,
  });

  // 处理刷新
  const handleRefresh = () => {
    refreshData();
  };

  // 判断是否正在加载
  const isLoading =
    loading.basic || loading.overview || loading.region || loading.timeline;

  // 网络图是否正在加载
  const isNetworkLoading = loading.network;

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          数字化统计分析
        </Title>

        {/* 筛选器 */}
        <StatisticsFilters
          selectedRegion={selectedRegion}
          dateRange={dateRange}
          loading={isLoading}
          onRegionChange={setSelectedRegion}
          onDateRangeChange={setDateRange}
          onRefresh={handleRefresh}
        />

        {/* 统计卡片 */}
        <StatisticsCards data={basicData || overviewData} loading={isLoading} />

        {/* 图表区域 */}
        <StatisticsCharts
          basicData={basicData}
          regionData={regionData}
          timelineData={timelineData}
          loading={isLoading}
        />

        {/* 关系网络图 */}
        <div style={{ marginTop: 24 }}>
          <NetworkGraphWithControls
            data={networkData}
            loading={isNetworkLoading}
            title="要素关系网络图"
            onRefresh={loadNetworkData}
          />
        </div>
      </div>
    </PublicLayout>
  );
};

export default DigitalPage;
