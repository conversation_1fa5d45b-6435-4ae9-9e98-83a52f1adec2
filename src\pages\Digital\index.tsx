import NetworkGraphWithControls from '@/components/NetworkGraph/NetworkGraphWithControls';
import PageHeader from '@/components/PageHeader';
import PublicLayout from '@/components/PublicLayout';
import React, { useMemo, useState } from 'react';
import StatisticsCards from './components/StatisticsCards';
import StatisticsCharts from './components/StatisticsCharts';
import StatisticsFilters from './components/StatisticsFilters';
import useDigitalStatistics from './hooks/useDigitalStatistics';

const DigitalPage: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [dateRange, setDateRange] = useState<any>(null);

  const regionId = useMemo(() => {
    if (selectedRegion === 'all') return undefined;
    const n = Number(selectedRegion);
    return Number.isFinite(n) ? n : undefined;
  }, [selectedRegion]);

  const startTime = useMemo(() => {
    return dateRange?.[0]?.toISOString();
  }, [dateRange]);

  const endTime = useMemo(() => {
    return dateRange?.[1]?.toISOString();
  }, [dateRange]);

  // 使用统计数据Hook
  const {
    basicData,
    regionData,
    timelineData,
    networkData,
    loading,
    refreshData,
    loadNetworkData,
  } = useDigitalStatistics({
    regionId,
    startTime,
    endTime,
    autoLoad: true,
  });

  // 处理刷新
  const handleRefresh = () => {
    refreshData();
  };

  // 判断是否正在加载
  const isLoading =
    loading.basic || loading.overview || loading.region || loading.timeline;

  // 网络图是否正在加载
  const isNetworkLoading = loading.network;

  return (
    <PublicLayout>
      {/* 页面头部Banner */}
      <PageHeader
        title="数字化统计分析"
        description="通过数据可视化技术，深入分析关中地区历史文化要素的分布规律和发展趋势。"
        backgroundType="digital"
        height={300}
      />

      <div className="content-card" style={{ padding: '24px' }}>

        {/* 筛选器 */}
        <StatisticsFilters
          selectedRegion={selectedRegion}
          dateRange={dateRange}
          loading={isLoading}
          onRegionChange={setSelectedRegion}
          onDateRangeChange={setDateRange}
          onRefresh={handleRefresh}
        />

        {/* 统计卡片 */}
        <StatisticsCards data={basicData} loading={isLoading} />

        {/* 图表区域 */}
        <StatisticsCharts
          basicData={basicData}
          regionData={regionData}
          timelineData={timelineData}
          loading={isLoading}
        />

        {/* 关系网络图 */}
        <div style={{ marginTop: 24 }}>
          <NetworkGraphWithControls
            data={networkData}
            loading={isNetworkLoading}
            title="要素关系网络图"
            onRefresh={loadNetworkData}
          />
        </div>
      </div>
    </PublicLayout>
  );
};

export default DigitalPage;
