import PhotoStatisticsComponent from '@/components/PhotoStatistics';
import { getAllRegionDict } from '@/services/dictionary';
import { getAllHistoricalElements } from '@/services/historicalElement';
import { getAllMountains } from '@/services/mountain';
import {
  deletePhoto,
  getPhotoList,
  getPhotoStatistics,
  updatePhoto,
  uploadFile,
  uploadUtils,
} from '@/services/upload';
import { getAllWaterSystems } from '@/services/waterSystem';
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  InboxOutlined,
  LoadingOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import {
  Button,
  Card,
  Form,
  Image,
  Input,
  message,
  Modal,
  Popconfirm,
  Progress,
  Radio,
  Space,
  Table,
  Tooltip,
  TreeSelect,
  Typography,
  Upload,
} from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';

const { Title } = Typography;
const { <PERSON>agger } = Upload;

const AdminUpload: React.FC = () => {
  const [data, setData] = useState<API.PhotoRecord[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<API.PhotoRecord | null>(null);
  const [form] = Form.useForm();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    [key: string]: number;
  }>({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<API.PhotoStatistics | null>(
    null,
  );
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 选项数据
  const [mountainOptions, setMountainOptions] = useState<
    Array<{ label: string; value: number }>
  >([]);
  const [waterSystemOptions, setWaterSystemOptions] = useState<
    Array<{ label: string; value: number }>
  >([]);
  const [historicalElementOptions, setHistoricalElementOptions] = useState<
    Array<{ label: string; value: number }>
  >([]);

  // 字典数据
  const [regionDict, setRegionDict] = useState<API.RegionDict[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');

  // 编辑表单状态
  const [editBindingType, setEditBindingType] = useState<
    'mountain' | 'waterSystem' | 'historicalElement' | null
  >(null);

  // 上传配置状态 - 改为TreeSelect格式
  const [uploadConfig, setUploadConfig] = useState({
    selectedValue: undefined as string | undefined, // TreeSelect的值，格式如 "region-1-mountain-2"
    entityType: undefined as
      | 'mountain'
      | 'waterSystem'
      | 'historicalElement'
      | undefined,
    entityId: undefined as number | undefined,
  });

  // 构建TreeSelect数据结构（用于上传配置）
  const buildTreeSelectData = () => {
    // 构建区域树
    const buildRegionTree = (parentId: number | null = null): any[] => {
      return regionDict
        .filter((item) => item.parentId === parentId)
        .map((region) => ({
          title: region.regionName,
          value: `region-${region.id}`,
          key: `region-${region.id}`,
          children: [
            ...buildRegionTree(region.id),
            // 在每个区域下添加固定的三个类别
            {
              title: '山塬',
              value: `region-${region.id}-category-mountain`,
              key: `region-${region.id}-category-mountain`,
              children: mountainOptions.map((mountain) => ({
                title: mountain.label,
                value: `region-${region.id}-mountain-${mountain.value}`,
                key: `region-${region.id}-mountain-${mountain.value}`,
              })),
            },
            {
              title: '水系',
              value: `region-${region.id}-category-waterSystem`,
              key: `region-${region.id}-category-waterSystem`,
              children: waterSystemOptions.map((waterSystem) => ({
                title: waterSystem.label,
                value: `region-${region.id}-waterSystem-${waterSystem.value}`,
                key: `region-${region.id}-waterSystem-${waterSystem.value}`,
              })),
            },
            {
              title: '历史要素',
              value: `region-${region.id}-category-historicalElement`,
              key: `region-${region.id}-category-historicalElement`,
              children: historicalElementOptions.map((element) => ({
                title: element.label,
                value: `region-${region.id}-historicalElement-${element.value}`,
                key: `region-${region.id}-historicalElement-${element.value}`,
              })),
            },
          ],
        }));
    };

    return buildRegionTree();
  };

  // 构建编辑界面的TreeSelect数据结构（不显示类别层）
  const buildEditTreeSelectData = (
    entityType: 'mountain' | 'waterSystem' | 'historicalElement',
  ) => {
    // 根据实体类型获取对应的选项
    let entityOptions: Array<{ label: string; value: number }> = [];
    switch (entityType) {
      case 'mountain':
        entityOptions = mountainOptions;
        break;
      case 'waterSystem':
        entityOptions = waterSystemOptions;
        break;
      case 'historicalElement':
        entityOptions = historicalElementOptions;
        break;
    }

    // 构建区域树，直接在区域下显示具体项目
    const buildRegionTree = (parentId: number | null = null): any[] => {
      return regionDict
        .filter((item) => item.parentId === parentId)
        .map((region) => ({
          title: region.regionName,
          value: `region-${region.id}`,
          key: `region-${region.id}`,
          disabled: true, // 区域节点不可选择
          children: [
            ...buildRegionTree(region.id),
            // 直接在区域下添加具体项目，不显示类别层
            ...entityOptions.map((entity) => ({
              title: entity.label,
              value: entity.value, // 直接使用实体ID作为value
              key: `region-${region.id}-${entityType}-${entity.value}`,
            })),
          ],
        }));
    };

    return buildRegionTree();
  };

  // 解析TreeSelect选择的值
  const parseSelectedValue = (value: string) => {
    if (!value) return { entityType: undefined, entityId: undefined };

    const parts = value.split('-');
    if (parts.length !== 4)
      return { entityType: undefined, entityId: undefined };

    const [, , entityType, entityId] = parts;
    return {
      entityType: entityType as
        | 'mountain'
        | 'waterSystem'
        | 'historicalElement',
      entityId: parseInt(entityId, 10),
    };
  };

  // 加载照片列表
  const loadPhotoList = async (page = 1, pageSize = 10, keyword = '') => {
    try {
      setLoading(true);
      const response = await getPhotoList({
        page,
        pageSize,
        keyword: keyword || undefined,
      });

      if (response.errCode === 0 && response.data) {
        setData(response.data.list);
        setPagination({
          current: response.data.page,
          pageSize: response.data.pageSize,
          total: response.data.total,
        });
      }
    } catch (error) {
      console.error('加载照片列表失败:', error);
      message.error('加载照片列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计信息
  const loadStatistics = async () => {
    try {
      const response = await getPhotoStatistics();
      if (response.errCode === 0 && response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  // 加载选项数据
  const loadOptions = async () => {
    try {
      // 加载字典数据
      const regionRes = await getAllRegionDict();

      if (regionRes.errCode === 0) {
        setRegionDict(regionRes.data || []);
      }

      // 加载山塬选项
      const mountainRes = await getAllMountains();
      if (mountainRes.errCode === 0) {
        setMountainOptions(
          mountainRes.data?.map((item) => ({
            label: item.name,
            value: item.id,
          })) || [],
        );
      }

      // 加载水系选项
      const waterSystemRes = await getAllWaterSystems();
      if (waterSystemRes.errCode === 0) {
        setWaterSystemOptions(
          waterSystemRes.data?.map((item) => ({
            label: item.name,
            value: item.id,
          })) || [],
        );
      }

      // 加载历史要素选项
      const historicalElementRes = await getAllHistoricalElements();
      if (historicalElementRes.errCode === 0) {
        setHistoricalElementOptions(
          historicalElementRes.data?.map((item) => ({
            label: item.name,
            value: item.id,
          })) || [],
        );
      }
    } catch (error) {
      console.error('加载选项数据失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadPhotoList();
    loadStatistics();
    loadOptions();
  }, []);

  const handleEdit = (record: API.PhotoRecord) => {
    setEditingItem(record);

    // 确定当前的绑定类型
    let bindingType: 'mountain' | 'waterSystem' | 'historicalElement' | null =
      null;
    if (record.mountainId) {
      bindingType = 'mountain';
    } else if (record.waterSystemId) {
      bindingType = 'waterSystem';
    } else if (record.historicalElementId) {
      bindingType = 'historicalElement';
    }

    setEditBindingType(bindingType);

    form.setFieldsValue({
      name: record.name,
      bindingType: bindingType,
      mountainId: record.mountainId,
      waterSystemId: record.waterSystemId,
      historicalElementId: record.historicalElementId,
    });
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      const response = await deletePhoto(id);
      if (response.errCode === 0) {
        message.success('删除成功！');
        loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
        loadStatistics();
      } else {
        message.error(response.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单数据:', values);

      if (editingItem) {
        // 构建更新参数，只包含实际需要的字段
        const updateParams: API.UpdatePhotoParams = {
          name: values.name,
        };

        // 根据绑定类型设置对应的ID，其他类型设置为undefined
        if (values.bindingType === 'mountain') {
          updateParams.mountainId = values.mountainId;
          updateParams.waterSystemId = undefined;
          updateParams.historicalElementId = undefined;
        } else if (values.bindingType === 'waterSystem') {
          updateParams.mountainId = undefined;
          updateParams.waterSystemId = values.waterSystemId;
          updateParams.historicalElementId = undefined;
        } else if (values.bindingType === 'historicalElement') {
          updateParams.mountainId = undefined;
          updateParams.waterSystemId = undefined;
          updateParams.historicalElementId = values.historicalElementId;
        } else {
          // 无关联
          updateParams.mountainId = undefined;
          updateParams.waterSystemId = undefined;
          updateParams.historicalElementId = undefined;
        }

        console.log('更新参数:', updateParams);
        const response = await updatePhoto(editingItem.id, updateParams);
        if (response.errCode === 0) {
          message.success('编辑成功！');
          setModalVisible(false);
          setEditBindingType(null);
          loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
          loadStatistics();
        } else {
          message.error(response.msg || '编辑失败');
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '预览',
      dataIndex: 'url',
      key: 'preview',
      width: 100,
      render: (url: string) => (
        <Image
          width={60}
          height={40}
          src={url}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          preview={{
            mask: <EyeOutlined />,
          }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '文件路径',
      dataIndex: 'path',
      key: 'path',
      ellipsis: true,
    },
    {
      title: '关联对象',
      key: 'related',
      render: (_: any, record: API.PhotoRecord) => {
        if (record.mountainId) {
          if (record.mountain) {
            return `山塬: ${record.mountain.name}`;
          }
          return `山塬: ID ${record.mountainId}`;
        }
        if (record.waterSystemId) {
          if (record.waterSystem) {
            return `水系: ${record.waterSystem.name}`;
          }
          return `水系: ID ${record.waterSystemId}`;
        }
        if (record.historicalElementId) {
          if (record.historicalElement) {
            return `历史要素: ${record.historicalElement.name}`;
          }
          return `历史要素: ID ${record.historicalElementId}`;
        }
        return '无关联';
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (createdAt: string) => {
        return createdAt ? new Date(createdAt).toLocaleString() : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_: any, record: any) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            style={{ padding: 0 }}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个文件吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              style={{ padding: 0 }}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 自定义上传函数
  const customUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options;

    // 验证文件
    const validation = uploadUtils.validateFile(file);
    if (!validation.valid) {
      message.error(validation.message);
      onError(new Error(validation.message));
      return;
    }

    try {
      setUploading(true);

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          const current = prev[file.uid] || 0;
          const next = Math.min(current + Math.random() * 30, 90);
          onProgress({ percent: next });
          return { ...prev, [file.uid]: next };
        });
      }, 200);

      // 调用上传接口，使用配置的参数
      const response = await uploadFile(file, {
        photoName: file.name,
        entityType: uploadConfig.entityType,
        entityId: uploadConfig.entityId,
      });

      clearInterval(progressInterval);

      if (response.errCode === 0 && response.data) {
        // 上传成功
        setUploadProgress((prev) => ({ ...prev, [file.uid]: 100 }));
        onProgress({ percent: 100 });
        onSuccess(response.data);

        const successMsg = response.data.photoId
          ? `${file.name} 上传成功，已创建照片记录 #${response.data.photoId}`
          : `${file.name} 上传成功`;
        message.success(successMsg);

        // 重新加载照片列表和统计信息
        loadPhotoList(pagination.current, pagination.pageSize, searchKeyword);
        loadStatistics();

        // 清除进度
        setTimeout(() => {
          setUploadProgress((prev) => {
            const newProgress = { ...prev };
            delete newProgress[file.uid];
            return newProgress;
          });
        }, 1000);
      } else {
        throw new Error(response.msg || '上传失败');
      }
    } catch (error: any) {
      console.error('上传失败:', error);
      message.error(error.message || `${file.name} 上传失败`);
      onError(error);
    } finally {
      setUploading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    customRequest: customUpload,
    fileList,
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onRemove: (file) => {
      setFileList((prev) => prev.filter((item) => item.uid !== file.uid));
    },
    beforeUpload: (file) => {
      const validation = uploadUtils.validateFile(file);
      if (!validation.valid) {
        message.error(validation.message);
        return false;
      }
      return true;
    },
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false,
    },
  };

  return (
    <div className="upload-container" style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: 24 }}>
        资源管理
      </Title>

      {/* 统计信息 */}
      <div style={{ marginBottom: 24 }}>
        <PhotoStatisticsComponent statistics={statistics} />
      </div>

      {/* 上传配置 */}
      <Card title="上传配置" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Space align="center" wrap>
              <span>关联对象：</span>
              <TreeSelect
                placeholder="选择区域 > 类别 > 具体项目"
                style={{
                  width: 300,
                  zIndex: 1000,
                }}
                value={uploadConfig.selectedValue}
                onChange={(value) => {
                  const parsed = parseSelectedValue(value);
                  setUploadConfig((prev) => ({
                    ...prev,
                    selectedValue: value,
                    entityType: parsed.entityType,
                    entityId: parsed.entityId,
                  }));
                }}
                treeData={buildTreeSelectData()}
                treeCheckable={false}
                showCheckedStrategy={TreeSelect.SHOW_CHILD}
                allowClear
                treeDefaultExpandAll={false}
                treeNodeFilterProp="title"
                showSearch
                filterTreeNode={(input, node) => {
                  return (node.title as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
                getPopupContainer={(triggerNode) =>
                  triggerNode.parentElement || document.body
                }
              />

              <span style={{ color: '#666', fontSize: '12px' }}>
                {uploadConfig.entityType && uploadConfig.entityId
                  ? '上传时会自动关联到选择的对象'
                  : '若需要直接关联对象，请按层级选择：区域 → 类别 → 具体项目'}
              </span>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 上传区域 */}
      <Card
        title={
          <div>
            文件上传{' '}
            <Tooltip
              title={
                <div className="upload-tips">
                  <div className="tips-title">📋 上传说明</div>
                  <ul className="tips-list">
                    <li>支持的图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP</li>
                    <li>单个文件最大大小：50MB</li>
                    <li>上传成功后可以编辑文件信息并关联到相关对象</li>
                    <li>文件将自动按日期分类存储</li>
                  </ul>
                </div>
              }
            >
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        }
        style={{ marginBottom: 24 }}
      >
        <Dragger {...uploadProps} style={{ padding: '20px' }}>
          <p className="ant-upload-drag-icon">
            {uploading ? <LoadingOutlined /> : <InboxOutlined />}
          </p>
          <p className="ant-upload-text">
            {uploading ? '正在上传...' : '点击或拖拽文件到此区域上传'}
          </p>
          <p className="ant-upload-hint">
            支持单个或批量上传。仅支持图片格式：JPG、JPEG、PNG、GIF、BMP、WEBP
            <br />
            单个文件最大 50MB
          </p>
        </Dragger>

        {/* 上传进度显示 */}
        {Object.keys(uploadProgress).length > 0 && (
          <div className="upload-progress">
            {Object.entries(uploadProgress).map(([uid, progress]) => {
              const file = fileList.find((f) => f.uid === uid);
              return file ? (
                <div key={uid} className="progress-item">
                  <div className="file-info">
                    <span className="file-name">{file.name}</span>
                    <span className="file-size">
                      {uploadUtils.formatFileSize(file.size || 0)}
                    </span>
                  </div>
                  <Progress percent={Math.round(progress)} size="small" />
                </div>
              ) : null;
            })}
          </div>
        )}
      </Card>

      {/* 照片列表 */}
      <Card
        title="照片列表"
        extra={
          <Space>
            <Input.Search
              placeholder="搜索照片名称"
              allowClear
              style={{ width: 200 }}
              onSearch={(value) => {
                setSearchKeyword(value);
                loadPhotoList(1, pagination.pageSize, value);
              }}
            />
            <Button
              type="default"
              onClick={() => {
                loadPhotoList();
                loadStatistics();
              }}
              size="small"
            >
              刷新数据
            </Button>
          </Space>
        }
      >
        <Table
          className="file-table"
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 张照片`,
            onChange: (page, pageSize) => {
              loadPhotoList(page, pageSize, searchKeyword);
            },
            onShowSizeChange: (_, size) => {
              loadPhotoList(1, size, searchKeyword);
            },
          }}
          size="middle"
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title="编辑照片信息"
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={600}
        destroyOnHidden
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="照片名称"
            rules={[{ required: true, message: '请输入照片名称' }]}
          >
            <Input placeholder="请输入照片名称" />
          </Form.Item>

          <Form.Item name="bindingType" label="绑定类别">
            <Radio.Group
              onChange={(e) => {
                const newType = e.target.value;
                setEditBindingType(newType);
                // 清空其他类型的选择
                if (newType !== 'mountain')
                  form.setFieldValue('mountainId', undefined);
                if (newType !== 'waterSystem')
                  form.setFieldValue('waterSystemId', undefined);
                if (newType !== 'historicalElement')
                  form.setFieldValue('historicalElementId', undefined);
              }}
            >
              <Radio value="mountain">山塬</Radio>
              <Radio value="waterSystem">水系</Radio>
              <Radio value="historicalElement">历史要素</Radio>
              <Radio value={null}>无关联</Radio>
            </Radio.Group>
          </Form.Item>

          {editBindingType === 'mountain' && (
            <Form.Item name="mountainId" label="选择山塬">
              <TreeSelect
                placeholder="选择区域 > 山塬"
                style={{ width: '100%' }}
                treeData={buildEditTreeSelectData('mountain')}
                treeCheckable={false}
                allowClear
                showSearch
                filterTreeNode={(input, node) => {
                  return (node.title as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
              />
            </Form.Item>
          )}

          {editBindingType === 'waterSystem' && (
            <Form.Item name="waterSystemId" label="选择水系">
              <TreeSelect
                placeholder="选择区域 > 水系"
                style={{ width: '100%' }}
                treeData={buildEditTreeSelectData('waterSystem')}
                treeCheckable={false}
                allowClear
                showSearch
                filterTreeNode={(input, node) => {
                  return (node.title as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
              />
            </Form.Item>
          )}

          {editBindingType === 'historicalElement' && (
            <Form.Item name="historicalElementId" label="选择历史要素">
              <TreeSelect
                placeholder="选择区域 > 历史要素"
                style={{ width: '100%' }}
                treeData={buildEditTreeSelectData('historicalElement')}
                treeCheckable={false}
                allowClear
                showSearch
                filterTreeNode={(input, node) => {
                  return (node.title as string)
                    .toLowerCase()
                    .includes(input.toLowerCase());
                }}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUpload;
