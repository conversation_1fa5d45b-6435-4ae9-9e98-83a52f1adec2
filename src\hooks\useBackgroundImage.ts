import { useEffect, useState } from 'react';

// 背景图配置
export const BACKGROUND_IMAGES = {
  hero: {
    default: '/backgrounds/hero-default.jpg',
    webp: '/backgrounds/hero-default.webp',
    mobile: '/backgrounds/hero-mobile.jpg',
    alt: '关中地区智慧营建系统',
  },
  mountain: {
    default: '/backgrounds/mountain-default.jpg',
    webp: '/backgrounds/mountain-default.webp',
    mobile: '/backgrounds/mountain-mobile.jpg',
    alt: '关中地区山塬地貌',
  },
  water: {
    default: '/backgrounds/water-default.jpg',
    webp: '/backgrounds/water-default.webp',
    mobile: '/backgrounds/water-mobile.jpg',
    alt: '关中地区水系分布',
  },
  history: {
    default: '/backgrounds/history-default.jpg',
    webp: '/backgrounds/history-default.webp',
    mobile: '/backgrounds/history-mobile.jpg',
    alt: '关中地区历史要素',
  },
  digital: {
    default: '/backgrounds/digital-default.jpg',
    webp: '/backgrounds/digital-default.webp',
    mobile: '/backgrounds/digital-mobile.jpg',
    alt: '数字化展示',
  },
};

// 检测WebP支持
const supportsWebP = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src =
      'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

// 检测移动设备
const isMobile = (): boolean => {
  return window.innerWidth <= 768;
};

// 预加载图片
const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

export interface UseBackgroundImageOptions {
  type: keyof typeof BACKGROUND_IMAGES;
  preload?: boolean;
  fallback?: string;
}

export interface BackgroundImageResult {
  imageUrl: string;
  isLoading: boolean;
  error: string | null;
  preloadNext: (nextType: keyof typeof BACKGROUND_IMAGES) => void;
}

export const useBackgroundImage = ({
  type,
  preload = true,
  fallback,
}: UseBackgroundImageOptions): BackgroundImageResult => {
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [webpSupported, setWebpSupported] = useState<boolean>(false);

  // 检测WebP支持
  useEffect(() => {
    supportsWebP().then(setWebpSupported);
  }, []);

  // 获取最佳图片URL
  const getBestImageUrl = (
    imageType: keyof typeof BACKGROUND_IMAGES,
  ): string => {
    const config = BACKGROUND_IMAGES[imageType];

    if (isMobile() && config.mobile) {
      return config.mobile;
    }

    if (webpSupported && config.webp) {
      return config.webp;
    }

    return config.default;
  };

  // 加载背景图
  useEffect(() => {
    if (!type) return;

    const loadImage = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const url = getBestImageUrl(type);

        if (preload) {
          await preloadImage(url);
        }

        setImageUrl(url);
      } catch (err) {
        console.error('Failed to load background image:', err);
        setError('图片加载失败');

        // 使用fallback图片
        if (fallback) {
          setImageUrl(fallback);
        } else {
          setImageUrl(BACKGROUND_IMAGES[type].default);
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadImage();
  }, [type, webpSupported, preload, fallback]);

  // 预加载下一张图片
  const preloadNext = (nextType: keyof typeof BACKGROUND_IMAGES) => {
    const url = getBestImageUrl(nextType);
    preloadImage(url).catch(console.error);
  };

  return {
    imageUrl,
    isLoading,
    error,
    preloadNext,
  };
};

// 背景图样式生成器
export const generateBackgroundStyle = (
  imageUrl: string,
  overlay?: string,
): React.CSSProperties => {
  const overlayGradient =
    overlay ||
    'linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%)';

  return {
    backgroundImage: `${overlayGradient}, url(${imageUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center center',
    backgroundRepeat: 'no-repeat',
    backgroundAttachment: window.innerWidth > 768 ? 'fixed' : 'scroll',
  };
};

// 页面特定的背景图Hook
export const useHeroBackground = () => useBackgroundImage({ type: 'hero' });
export const useMountainBackground = () =>
  useBackgroundImage({ type: 'mountain' });
export const useWaterBackground = () => useBackgroundImage({ type: 'water' });
export const useHistoryBackground = () =>
  useBackgroundImage({ type: 'history' });
export const useDigitalBackground = () =>
  useBackgroundImage({ type: 'digital' });
