/**
 * @file 统计卡片组件
 * @description 数字化统计页面的统计卡片组件
 * <AUTHOR> Assistant
 * @date 2025-09-09
 */

import type { BasicStatisticsData } from '@/services/portal';
import {
  // BarChartOutlined,
  DatabaseOutlined,
  EnvironmentOutlined,
  GlobalOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';

export interface StatisticsCardsProps {
  data?: BasicStatisticsData | null;
  loading?: boolean;
}

/**
 * 统计卡片组件
 */
export const StatisticsCards: React.FC<StatisticsCardsProps> = ({
  data,
  loading = false,
}) => {
  const counts = data?.counts;

  if (!counts) {
    return null;
  }

  // 基础统计卡片
  const basicCards = [
    {
      title: '山塬总数',
      value: counts.mountain || 0,
      icon: <EnvironmentOutlined style={{ color: '#52c41a' }} />,
      color: '#52c41a',
    },
    {
      title: '水系总数',
      value: counts.waterSystem || 0,
      icon: <GlobalOutlined style={{ color: '#1890ff' }} />,
      color: '#1890ff',
    },
    {
      title: '历史要素总数',
      value: counts.historicalElement || 0,
      icon: <HistoryOutlined style={{ color: '#fa8c16' }} />,
      color: '#fa8c16',
    },
    {
      title: '数据总量',
      value:
        (counts.mountain || 0) +
        (counts.waterSystem || 0) +
        (counts.historicalElement || 0),
      icon: <DatabaseOutlined style={{ color: '#722ed1' }} />,
      color: '#722ed1',
    },
  ];

  // 扩展统计卡片
  // const extendedCards = [
  //   {
  //     title: '用户数量',
  //     value: counts.user || 0,
  //     icon: <UserOutlined style={{ color: '#13c2c2' }} />,
  //     color: '#13c2c2',
  //   },
  //   {
  //     title: '类型字典',
  //     value: counts.typeDict || 0,
  //     icon: <BarChartOutlined style={{ color: '#eb2f96' }} />,
  //     color: '#eb2f96',
  //   },
  //   {
  //     title: '区域字典',
  //     value: counts.regionDict || 0,
  //     icon: <EnvironmentOutlined style={{ color: '#f5222d' }} />,
  //     color: '#f5222d',
  //   },
  //   {
  //     title: '关系字典',
  //     value: counts.relationshipDict || 0,
  //     icon: <GlobalOutlined style={{ color: '#52c41a' }} />,
  //     color: '#52c41a',
  //   },
  // ];

  return (
    <>
      {/* 基础统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {basicCards.map((card, index) => (
          <Col xs={24} sm={12} md={6} key={index}>
            <Card loading={loading}>
              <Statistic
                title={card.title}
                value={card.value}
                prefix={card.icon}
                valueStyle={{ color: card.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 扩展统计卡片 - 只有当有扩展数据时才显示 */}
      {/* {'user' in counts && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          {extendedCards.map((card, index) => (
            <Col xs={24} sm={12} md={6} key={index}>
              <Card loading={loading}>
                <Statistic
                  title={card.title}
                  value={card.value}
                  prefix={card.icon}
                  valueStyle={{ color: card.color }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      )} */}
    </>
  );
};

export default StatisticsCards;
